"use client";

import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import Link from 'next/link';
import GameCard, { GameCardSkeleton } from '@/components/features/game/GameCard';
import { GameFrontmatter } from '@/lib/types';
import { ChevronLeft, ChevronRight } from 'lucide-react';

// 热度标签类型定义（与GameCard保持一致）
type HotTag = 'hot' | 'new' | 'top_rated';

// 游戏数据接口（适配现有的GameFrontmatter）
interface GameData extends GameFrontmatter {
  gameId?: string;
  coverImage?: string;
}

// 组件Props接口
interface RelatedGamesCarouselProps {
  /** 游戏数据列表 */
  games: GameData[];
  /** 轮播标题 */
  title?: string;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 自动播放间隔（毫秒） */
  autoPlayInterval?: number;
  /** 游戏点击回调 */
  onGameClick?: (gameId: string) => void;
  /** 加载状态 */
  loading?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 卡片尺寸 */
  cardSize?: 'small' | 'medium' | 'large';
  /** 是否显示热度标签 */
  showTags?: boolean;
  /** 点击跳转链接 */
  linkTo?: string;
}

// 骨架屏组件
const CarouselSkeleton = memo(({ cardSize = 'medium' }: { cardSize?: 'small' | 'medium' | 'large' }) => (
  <div className="overflow-hidden">
    <div className="flex gap-2 transition-transform duration-300">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="flex-shrink-0">
          <GameCardSkeleton size={cardSize} />
        </div>
      ))}
    </div>
  </div>
));

CarouselSkeleton.displayName = 'CarouselSkeleton';

// 主组件
const RelatedGamesCarousel = memo<RelatedGamesCarouselProps>(({
  games,
  title = "Related Games",
  autoPlay = false,
  autoPlayInterval = 3000,
  onGameClick,
  loading = false,
  className = '',
  cardSize = 'medium',
  showTags = true,
  linkTo
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  
  const carouselRef = useRef<HTMLDivElement>(null);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartRef = useRef<number>(0);

  // 卡片尺寸配置 - 压缩尺寸以显示更多卡片
  const cardSizeConfig = {
    small: { width: 160, gap: 6 },
    medium: { width: 200, gap: 8 },
    large: { width: 240, gap: 10 }
  };

  const { width: cardWidth, gap } = cardSizeConfig[cardSize];

  // 动态计算容器高度
  const getContainerHeight = useCallback(() => {
    const containerHeight = {
      small: 88,     // 84px 卡片 + 4px padding
      medium: 100,   // 96px 卡片 + 4px padding  
      large: 112     // 108px 卡片 + 4px padding
    };
    return containerHeight[cardSize];
  }, [cardSize]);

  // 计算可见卡片数量 - 增加桌面端显示数量
  const getVisibleCards = useCallback(() => {
    if (typeof window === 'undefined') return 6;
    
    const screenWidth = window.innerWidth;
    if (screenWidth < 640) return 1; // 移动端
    if (screenWidth < 768) return 2; // 小平板
    if (screenWidth < 1024) return 4; // 平板
    if (screenWidth < 1440) return 6; // 中等桌面端
    return 8; // 大桌面端
  }, []);

  const [visibleCards, setVisibleCards] = useState(getVisibleCards);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      setVisibleCards(getVisibleCards());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [getVisibleCards]);

  // 扩展游戏列表以实现无限循环
  const extendedGames = React.useMemo(() => {
    if (!games || games.length === 0) return [];
    if (games.length <= visibleCards) return games;
    
    // 为无限循环添加前后的卡片
    const cloneCount = Math.max(visibleCards, 3);
    const frontClones = games.slice(-cloneCount);
    const backClones = games.slice(0, cloneCount);
    
    return [...frontClones, ...games, ...backClones];
  }, [games, visibleCards]);

  const totalCards = extendedGames.length;
  const originalLength = games.length;
  const cloneCount = Math.max(visibleCards, 3);

  // 自动播放逻辑
  const startAutoPlay = useCallback(() => {
    if (!autoPlay || isHovered || isDragging || totalCards <= visibleCards) return;
    
    autoPlayRef.current = setInterval(() => {
      setCurrentIndex(prev => prev + 1);
    }, autoPlayInterval);
  }, [autoPlay, isHovered, isDragging, autoPlayInterval, totalCards, visibleCards]);

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  }, []);

  // 处理无限循环的索引重置
  useEffect(() => {
    if (totalCards <= visibleCards) return;

    if (currentIndex >= cloneCount + originalLength) {
      // 到达末尾，重置到开始
      setTimeout(() => {
        setCurrentIndex(cloneCount);
      }, 300);
    } else if (currentIndex < cloneCount) {
      // 到达开始，重置到末尾
      setTimeout(() => {
        setCurrentIndex(cloneCount + originalLength - 1);
      }, 300);
    }
  }, [currentIndex, totalCards, originalLength, cloneCount, visibleCards]);

  // 自动播放控制
  useEffect(() => {
    startAutoPlay();
    return stopAutoPlay;
  }, [startAutoPlay, stopAutoPlay]);

  // 手动导航
  const goToPrevious = useCallback(() => {
    if (totalCards <= visibleCards) return;
    stopAutoPlay();
    setCurrentIndex(prev => prev - 1);
    setTimeout(startAutoPlay, 1000);
  }, [stopAutoPlay, startAutoPlay, totalCards, visibleCards]);

  const goToNext = useCallback(() => {
    if (totalCards <= visibleCards) return;
    stopAutoPlay();
    setCurrentIndex(prev => prev + 1);
    setTimeout(startAutoPlay, 1000);
  }, [stopAutoPlay, startAutoPlay, totalCards, visibleCards]);

  // 触摸事件处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartRef.current = e.touches[0].clientX;
    setIsDragging(true);
    stopAutoPlay();
  }, [stopAutoPlay]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
  }, [isDragging]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const touchEnd = e.changedTouches[0].clientX;
    const diff = touchStartRef.current - touchEnd;
    const threshold = 50;

    if (Math.abs(diff) > threshold) {
      if (diff > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    }

    setIsDragging(false);
    setTimeout(startAutoPlay, 1000);
  }, [isDragging, goToNext, goToPrevious, startAutoPlay]);

  // 鼠标事件处理
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    stopAutoPlay();
  }, [stopAutoPlay]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    if (!isDragging) {
      startAutoPlay();
    }
  }, [isDragging, startAutoPlay]);

  // 游戏点击处理
  const handleGameClick = useCallback((game: GameData) => {
    const gameId = game.slug || game.gameId || '';
    onGameClick?.(gameId);
  }, [onGameClick]);

  // 生成热度标签
  const getGameTags = useCallback((game: GameData, index: number): HotTag[] => {
    if (!showTags) return [];
    
    if (index === 0) return ['hot'];
    if (game.publishedDate && new Date(game.publishedDate) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
      return ['new'];
    }
    return [];
  }, [showTags]);

  // 计算变换样式
  const getTransformStyle = () => {
    const translateX = -currentIndex * (cardWidth + gap);
    return {
      transform: `translateX(${translateX}px)`,
      transition: isDragging ? 'none' : 'transform 0.3s ease-in-out'
    };
  };

  // 如果没有游戏数据
  if (!loading && (!games || games.length === 0)) {
    return (
      <section 
        className={`p-4 ${className}`}
        data-noindex="true"
        aria-hidden="true"
      >
        <div className="text-center py-8 text-muted-foreground">
          <p className="text-muted-foreground">No related games available</p>
        </div>
      </section>
    );
  }

  // 轮播容器内容
  return (
    <section 
      className={`p-1 ${className}`} 
      role="region" 
      aria-label={title}
      data-noindex="true"
      aria-hidden="true"
    >
      {/* 轮播容器 - 设置固定高度确保卡片正常显示 */}
      <div className="relative" style={{ height: `${getContainerHeight()}px` }}>
        {/* 加载状态 */}
        {loading ? (
          <CarouselSkeleton cardSize={cardSize} />
        ) : (
          <>
            {/* 轮播内容 */}
            <div
              ref={carouselRef}
              className="overflow-hidden w-full h-full"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <div
                className={`flex w-full h-full`}
                style={{
                  ...getTransformStyle(),
                  gap: `${gap}px` // 使用动态间距值而不是固定的gap-1
                }}
              >
                {extendedGames.map((game, index) => (
                  <div
                    key={`${game.slug || game.gameId}-${index}`}
                    className="flex-shrink-0"
                    style={{ width: cardWidth, height: `${getContainerHeight() - 4}px` }}
                  >
                    <GameCard
                      game={game}
                      size={cardSize}
                      tags={getGameTags(game, index % originalLength)}
                      lazy={Math.abs(index - currentIndex) > visibleCards}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* 导航按钮 - 调整位置到左右两边 */}
            {totalCards > visibleCards && (
              <>
                {/* 左箭头 */}
                <button
                  onClick={goToPrevious}
                  className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 z-10 w-10 h-10 bg-background/50 hover:bg-background/70 text-foreground rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
                  aria-label="Previous game"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                {/* 右箭头 */}
                <button
                  onClick={goToNext}
                  className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 z-10 w-10 h-10 bg-background/50 hover:bg-background/70 text-foreground rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
                  aria-label="Next game"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}
          </>
        )}
      </div>
    </section>
  );
});

RelatedGamesCarousel.displayName = 'RelatedGamesCarousel';

export default RelatedGamesCarousel;
export type { RelatedGamesCarouselProps, GameData }; 