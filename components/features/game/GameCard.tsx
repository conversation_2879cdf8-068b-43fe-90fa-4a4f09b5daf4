"use client";

import React, { useState, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { GameFrontmatter } from '@/lib/types';
import { generateGameCoverAlt } from '@/lib/utils/alt-text-utils';
import BookmarkButton from '@/components/common/ui/BookmarkButton';

// 热度标签类型定义
type HotTag = 'hot' | 'new' | 'top_rated';

// 尺寸变体定义
type CardSize = 'small' | 'medium' | 'large' | 'sidebar';

interface GameCardProps {
  /** 游戏数据 */
  game: GameFrontmatter;
  /** 热度标签 */
  tags?: HotTag[];
  /** 卡片尺寸 */
  size?: CardSize;
  /** 自定义点击处理 */
  onClick?: (gameId: string) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否启用懒加载 */
  lazy?: boolean;
}

// 基于游戏slug生成固定的随机评分
const generateFixedRating = (slug: string): number => {
  let hash = 0;
  for (let i = 0; i < slug.length; i++) {
    const char = slug.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  // 将哈希值转换为7.5-10.0范围的评分
  const normalized = Math.abs(hash) % 250; // 0-249
  return Number((7.5 + (normalized / 100)).toFixed(1));
};

// 简化的星级评分组件
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex items-center gap-1">
      <span className="text-accent text-sm drop-shadow-lg">⭐</span>
      <span className="text-accent-foreground text-sm font-bold drop-shadow-lg">{rating}</span>
    </div>
  );
};

// 骨架屏组件
function GameCardSkeleton({ size = 'medium' }: { size?: CardSize }) {
  const sizeClasses = {
    small: 'w-[140px] h-[84px]',
    medium: 'w-[160px] h-[96px]',
    large: 'w-[180px] h-[108px]',
    sidebar: 'w-[180px] h-[140px]'
  };

  return (
    <div className={`${sizeClasses[size]} mx-auto`}>
      <div className="w-full h-full theme-skeleton rounded-lg overflow-hidden shadow-sm animate-pulse">
        <div className="w-full h-full theme-skeleton-dark" />
      </div>
    </div>
  );
}

// 热度标签组件
function HotTagBadge({ tag }: { tag: HotTag }) {
  const tagConfig = {
    hot: {
      label: 'HOT',
      className: 'theme-badge-hot',
      icon: '🔥'
    },
    new: {
      label: 'NEW',
      className: 'theme-badge-new',
      icon: '✨'
    },
    top_rated: {
      label: 'TOP RATED',
      className: 'theme-badge-featured',
      icon: '⭐'
    }
  };

  const config = tagConfig[tag];
  
  // 如果配置不存在，返回null
  if (!config) {
    return null;
  }

  return (
    <div className={`
      absolute top-2 left-2 z-10 theme-badge
      shadow-md backdrop-blur-sm ${config.className}
      flex items-center gap-1
    `}>
      <span className="text-xs">{config.icon}</span>
      <span>{config.label}</span>
    </div>
  );
}

export default function GameCard({
  game,
  tags = [],
  size = 'medium',
  onClick,
  className = '',
  lazy = true
}: GameCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // 空值检查
  if (!game) {
    return <GameCardSkeleton size={size} />;
  }

  const {
    title = 'Untitled Game',
    displayTitle,
    slug = '',
    thumbnail = '/images/thumbnails/default.svg',
    genres = [],
    description = 'No description available'
  } = game;

  // 获取显示标题，优先使用displayTitle，回退到title
  const getDisplayTitle = () => {
    return displayTitle || title;
  };

  // 生成固定的随机评分
  const gameRating = useMemo(() => generateFixedRating(slug), [slug]);

  // 无效数据检查
  if (!slug) {
    return <GameCardSkeleton size={size} />;
  }

  // 尺寸样式配置 - 改为矩形卡片
  const sizeClasses = {
    small: {
      container: 'w-[140px] h-[84px]',
      title: 'text-xs'
    },
    medium: {
      container: 'w-[160px] h-[96px]',
      title: 'text-sm'
    },
    large: {
      container: 'w-[180px] h-[108px]',
      title: 'text-base'
    },
    sidebar: {
      container: 'w-[180px] h-[140px]',
      title: 'text-sm'
    }
  };

  const currentSize = sizeClasses[size];

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(slug);
    }
  };

  // 处理图片加载
  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <div className={`${currentSize.container} mx-auto ${className}`}>
      <Link 
        href={`/${slug}`}
        onClick={handleClick}
        className="group block w-full h-full focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-lg"
        aria-label={`Play ${title} - ${description}`}
      >
        <article className="
          relative w-full h-full theme-card rounded-lg overflow-hidden
          cursor-pointer
          transition-all duration-300 ease-in-out 
          hover:scale-105 hover:shadow-xl hover:border-primary
        ">
          {/* 热度标签 */}
          {tags.length > 0 && (
            <HotTagBadge tag={tags[0]} />
          )}

          {/* 收藏按钮 */}
          <div className="absolute top-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <BookmarkButton 
              game={game} 
              size="small"
              className="bg-background/20 backdrop-blur-sm rounded-full"
            />
          </div>

          {/* 主图片 - 填满整个卡片 */}
          <Image
            src={imageError ? '/images/thumbnails/default.svg' : thumbnail}
            alt={generateGameCoverAlt(title, slug)}
            fill
            className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
            sizes="180px"
            loading={lazy ? 'lazy' : 'eager'}
            onLoad={handleImageLoad}
            onError={handleImageError}
            priority={!lazy}
          />

          {/* 悬停遮罩层 */}
          <div className="
            absolute inset-0 
            bg-background/0 group-hover:bg-background/60
            transition-all duration-300 ease-in-out
          " />

          {/* 游戏信息 - 仅在悬停时显示 */}
          <div className="
            absolute bottom-0 left-0 right-0
            bg-gradient-to-t from-background/90 via-background/70 to-transparent
            p-3
            transform translate-y-full group-hover:translate-y-0
            transition-all duration-300 ease-in-out
            opacity-0 group-hover:opacity-100
          ">
            <div className="text-left">
              {/* 游戏名称 */}
              <div className={`
                text-foreground font-bold 
                ${currentSize.title} leading-tight
                line-clamp-2 mb-2
                drop-shadow-lg
              `}>
                {getDisplayTitle()}
              </div>
              
              {/* 星级评分 */}
              <div className="flex items-center justify-between">
                <StarRating rating={gameRating} />
                
                {/* 类型标签 */}
                {genres.length > 0 && (
                  <span className="text-xs theme-text-muted bg-background/20 px-2 py-1 rounded-full backdrop-blur-sm">
                    {genres[0]}
                  </span>
                )}
              </div>
            </div>
          </div>


        </article>
      </Link>
    </div>
  );
}

// 导出骨架屏组件供外部使用
export { GameCardSkeleton }; 
