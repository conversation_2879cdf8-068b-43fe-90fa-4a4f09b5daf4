# 游戏网站项目

这是一个基于Next.js构建的游戏网站项目，提供游戏展示、博客文章等功能。

## 项目结构

```
/
├── app/                    # Next.js App Router
│   ├── (content)/          # 内容相关路由
│   │   ├── blog/           # 博客路由
│   │   ├── games/          # 游戏分类路由
│   │   ├── hot-games/      # 热门游戏路由
│   │   └── new-games/      # 新游戏路由
│   ├── (features)/         # 功能相关路由
│   │   └── favorites/      # 收藏夹功能
│   ├── (marketing)/        # 营销页面路由
│   │   ├── about-us/       # 关于我们
│   │   ├── contact-us/     # 联系我们
│   │   └── ...             # 其他营销页面
│   └── [game-slug]/        # 游戏详情页动态路由
├── components/             # React组件
│   ├── common/             # 通用组件
│   │   ├── ads/            # 广告相关组件
│   │   └── ui/             # UI通用组件
│   ├── features/           # 功能组件
│   │   └── game/           # 游戏相关组件
│   └── layout/             # 布局组件
│       ├── footer/         # 页脚组件
│       └── header/         # 页头组件
├── config/                 # 配置文件
├── content/                # 内容文件
│   ├── blog/               # 博客文章(Markdown)
│   ├── games/              # 游戏内容(Markdown)
│   └── pages/              # 静态页面内容
├── hooks/                  # React自定义钩子
├── lib/                    # 工具库
│   ├── api/                # API相关函数
│   ├── constants/          # 常量定义
│   ├── hooks/              # 通用钩子
│   └── utils/              # 工具函数
├── public/                 # 静态资源
│   └── images/             # 图片资源
│       ├── blog/           # 博客图片
│       ├── games/          # 游戏图片
│       ├── ui/             # UI图片
│       └── icons/          # 图标
├── scripts/                # 脚本文件
│   ├── data/               # 脚本数据
│   └── templates/          # 脚本模板
├── styles/                 # 样式文件
└── types/                  # TypeScript类型定义
```

## 图片资源管理

项目图片资源主要通过Cloudflare R2存储和访问，本地public目录仅存储少量必要的静态资源。

## 内容管理

- 游戏内容: `content/games/`目录下的Markdown文件
- 博客文章: `content/blog/`目录下的Markdown文件
- 静态页面: `content/pages/`目录下的Markdown文件

## 开发命令

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建项目
pnpm build

# 启动生产服务
pnpm start

# 添加游戏内容
pnpm add-games
```