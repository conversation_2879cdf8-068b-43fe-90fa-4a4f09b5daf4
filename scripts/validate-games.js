const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// 定义常量
const GAMES_DIR = path.join(__dirname, '../games');
const THUMBNAILS_DIR = path.join(__dirname, '../public/images/thumbnails');
const REQUIRED_FIELDS = ['title', 'slug', 'thumbnail', 'genres', 'iframeUrl', 'description'];

/**
 * 主验证函数
 */
function validateGames() {
  console.log('🚀 开始验证游戏内容文件...');

  let errorCount = 0;

  try {
    // 1. 读取所有游戏文件
    const files = fs.readdirSync(GAMES_DIR).filter(file => file.endsWith('.md'));
    console.log(`🔍 找到 ${files.length} 个游戏文件进行验证。`);

    // 2. 遍历并验证每个文件
    files.forEach(file => {
      const filePath = path.join(GAMES_DIR, file);
      
      try {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const { data: frontmatter } = matter(fileContent);

        // a. 验证必填字段
        REQUIRED_FIELDS.forEach(field => {
          if (frontmatter[field] === undefined || frontmatter[field] === null || frontmatter[field] === '') {
            throw new Error(`缺少必填字段或字段为空: "${field}"`);
          }
        });

        // b. 验证 thumbnail 文件是否存在
        const thumbnailPath = path.join(THUMBNAILS_DIR, path.basename(frontmatter.thumbnail));
        if (!fs.existsSync(thumbnailPath)) {
          throw new Error(`缩略图文件不存在: "${frontmatter.thumbnail}". 检查路径: ${thumbnailPath}`);
        }

      } catch (error) {
        console.error(`❌ 文件 [${file}] 验证失败: ${error.message}`);
        errorCount++;
      }
    });

    // 3. 输出最终结果
    if (errorCount > 0) {
      console.error(`\n💥 验证完成，发现 ${errorCount} 个错误。请根据上面的提示进行修复。`);
      process.exit(1); // 以错误码退出
    } else {
      console.log('\n✅ 所有游戏文件均通过验证，内容结构完整！');
      process.exit(0); // 正常退出
    }

  } catch (error) {
    console.error(`💥 执行验证脚本时发生严重错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行验证
validateGames(); 