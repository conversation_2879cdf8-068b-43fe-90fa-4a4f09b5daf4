"use client";

import React from 'react';

interface RadiusShowcaseProps {
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 圆角展示组件，展示theme.css中定义的所有圆角变量
 */
export default function RadiusShowcase({ className = '' }: RadiusShowcaseProps) {
  // 圆角变量列表
  const radiuses = [
    { name: 'radius-sm', variable: 'var(--radius-sm)' },
    { name: 'radius-md', variable: 'var(--radius-md)' },
    { name: 'radius-lg', variable: 'var(--radius-lg)' },
    { name: 'radius-xl', variable: 'var(--radius-xl)' },
  ];

  return (
    <div className={`p-6 theme-card ${className}`}>
      <h3 className="text-lg font-medium mb-4">圆角变量展示</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {radiuses.map((radius, index) => (
          <div key={index} className="flex flex-col items-center">
            <div 
              className="w-24 h-24 bg-primary mb-2"
              style={{ borderRadius: radius.variable }}
            />
            <div className="text-sm font-medium">{radius.name}</div>
            <div className="text-xs text-muted-foreground">{radius.variable}</div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 text-sm text-muted-foreground">
        此组件展示了theme.css中定义的所有圆角变量
      </div>
    </div>
  );
}