const Utils = require('./utils');

/**
 * 内容质量检查器 - 适配英文内容和六年级英语水平
 */
class ContentQualityChecker {
  constructor() {
    this.qualityRules = {
      title: {
        minLength: 10,
        maxLength: 60,
        requiredKeywords: [] // 移除中文关键词要求
      },
      description: {
        minLength: 120,
        maxLength: 160,
        requiredKeywords: ['free', 'online', 'play'] // 改为英文关键词
      },
      content: {
        minLength: 400,
        maxLength: 2000, // 适当减少，因为六年级英语用词简单
        minParagraphs: 2
      },
      keywordDensity: {
        min: 3.0, // 最低3%
        max: 4.5, // 最高4.5%
        target: 3.5 // 目标密度
      },
      englishLevel: {
        maxWordsPerSentence: 15, // 六年级英语：每句最多15个单词
        maxSyllablesPerWord: 3,  // 简单词汇：最多3个音节
        requiredReadabilityScore: 80 // Flesch Reading Ease分数要求
      }
    };
  }

  checkQuality(content, gameData) {
    const results = {
      overall: 0,
      issues: [],
      warnings: [],
      suggestions: [],
      details: {}
    };

    results.details.title = this.checkTitle(content.seoTitle, gameData);
    results.details.description = this.checkDescription(content.metaDescription, gameData);
    results.details.content = this.checkContent(content, gameData);
    results.details.keywords = this.checkKeywordDensity(content, gameData);
    results.details.englishLevel = this.checkEnglishLevel(content, gameData);
    results.details.duplicates = this.checkDuplicates(content);
    results.details.yamlSafety = this.checkYamlSafety(content);
    
    results.overall = this.calculateOverallScore(results.details);
    this.collectIssuesAndSuggestions(results);
    
    return results;
  }

  checkTitle(title, gameData) {
    const result = {
      score: 0,
      length: title.length,
      issues: [],
      suggestions: []
    };

    // 标题长度检查
    if (title.length < this.qualityRules.title.minLength) {
      result.issues.push(`Title too short (${title.length} chars, min ${this.qualityRules.title.minLength})`);
    } else if (title.length > this.qualityRules.title.maxLength) {
      result.issues.push(`Title too long (${title.length} chars, max ${this.qualityRules.title.maxLength})`);
    } else {
      result.score += 40;
    }

    // 检查是否包含游戏名称
    if (title.includes(gameData.name)) {
      result.score += 30;
    } else {
      result.issues.push('Title should contain the game name');
    }

    // 检查英文可读性
    const readabilityScore = this.checkTextReadability(title);
    if (readabilityScore >= this.qualityRules.englishLevel.requiredReadabilityScore) {
      result.score += 30;
    } else {
      result.suggestions.push('Use simpler words in title for better readability');
    }

    return result;
  }

  checkDescription(description, gameData) {
    const result = {
      score: 0,
      length: description.length,
      issues: [],
      suggestions: []
    };

    // 描述长度检查
    if (description.length < this.qualityRules.description.minLength) {
      result.issues.push(`Description too short (${description.length} chars, min ${this.qualityRules.description.minLength})`);
    } else if (description.length > this.qualityRules.description.maxLength) {
      result.issues.push(`Description too long (${description.length} chars, max ${this.qualityRules.description.maxLength})`);
    } else {
      result.score += 30;
    }

    // 检查必需的英文关键词
    const keywordScore = this.checkRequiredKeywords(description, this.qualityRules.description.requiredKeywords);
    result.score += keywordScore.score;
    result.issues.push(...keywordScore.issues);

    // 检查是否包含游戏类型
    const genreIncluded = gameData.genres.some(genre => 
      description.toLowerCase().includes(genre.toLowerCase())
    );
    if (genreIncluded) {
      result.score += 20;
    } else {
      result.suggestions.push('Include game genre in description');
    }

    // 检查行动召唤词汇
    const callToActionWords = ['play', 'start', 'try', 'enjoy', 'experience'];
    const hasCallToAction = callToActionWords.some(word => 
      description.toLowerCase().includes(word)
    );
    if (hasCallToAction) {
      result.score += 20;
    } else {
      result.suggestions.push('Add call-to-action words like "play", "try", "enjoy"');
    }

    return result;
  }

  checkContent(content, gameData) {
    const result = {
      score: 0,
      totalLength: 0,
      issues: [],
      suggestions: []
    };

    const contentParts = [
      content.introductionContent,
      content.featuresContent,
      content.howToPlayContent,
      content.controlsContent,
      content.tipsContent,
      content.similarGamesContent
    ];

    const allText = contentParts.join(' ');
    result.totalLength = allText.length;

    // 内容长度检查
    if (result.totalLength < this.qualityRules.content.minLength) {
      result.issues.push(`Content too short (${result.totalLength} chars, min ${this.qualityRules.content.minLength})`);
    } else if (result.totalLength > this.qualityRules.content.maxLength) {
      result.issues.push(`Content too long (${result.totalLength} chars, max ${this.qualityRules.content.maxLength})`);
    } else {
      result.score += 25;
    }

    // 检查必需部分
    const requiredSections = ['introductionContent', 'featuresContent', 'howToPlayContent', 'controlsContent', 'tipsContent'];
    const missingSections = requiredSections.filter(section => 
      !content[section] || content[section].trim().length < 30
    );
    
    if (missingSections.length === 0) {
      result.score += 25;
    } else {
      result.issues.push(`Missing or insufficient sections: ${missingSections.join(', ')}`);
    }

    // 检查段落结构
    const paragraphCount = content.introductionContent.split('\n\n').length;
    if (paragraphCount >= this.qualityRules.content.minParagraphs) {
      result.score += 15;
    } else {
      result.suggestions.push('Add more paragraphs for better readability');
    }

    // 检查是否有emoji图标
    const hasEmojis = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(content.featuresContent);
    if (hasEmojis) {
      result.score += 10;
    } else {
      result.suggestions.push('Add emoji icons to features section');
    }

    // 检查英文可读性
    const readabilityScore = this.checkTextReadability(allText);
    if (readabilityScore >= this.qualityRules.englishLevel.requiredReadabilityScore) {
      result.score += 25;
    } else {
      result.suggestions.push('Use simpler English words and shorter sentences');
    }

    return result;
  }

  checkKeywordDensity(content, gameData) {
    const result = {
      score: 0,
      density: {},
      issues: [],
      warnings: [],
      suggestions: []
    };

    const allText = Object.values(content).join(' ').toLowerCase();
    const words = allText.split(/\s+/).filter(word => word.length > 0);
    const totalWords = words.length;

    // 主要关键词：游戏名称和游戏类型
    const mainKeywords = [
      gameData.name.toLowerCase(),
      ...gameData.genres.map(g => g.toLowerCase())
    ];

    // 检查每个关键词的密度
    mainKeywords.forEach(keyword => {
      const keywordRegex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      const matches = allText.match(keywordRegex) || [];
      const count = matches.length;
      const density = totalWords > 0 ? (count / totalWords * 100) : 0;
      
      result.density[keyword] = { 
        count, 
        density: parseFloat(density.toFixed(2)),
        target: this.qualityRules.keywordDensity.target
      };

      // 严格的密度检查
      if (density >= this.qualityRules.keywordDensity.min && 
          density <= this.qualityRules.keywordDensity.max) {
        result.score += 20;
      } else if (density > this.qualityRules.keywordDensity.max) {
        result.issues.push(`Keyword "${keyword}" density too high (${density.toFixed(2)}%, max ${this.qualityRules.keywordDensity.max}%)`);
      } else if (density < this.qualityRules.keywordDensity.min) {
        result.issues.push(`Keyword "${keyword}" density too low (${density.toFixed(2)}%, min ${this.qualityRules.keywordDensity.min}%)`);
      }
    });

    // 检查辅助关键词
    const supportKeywords = ['free', 'online', 'play', 'game', 'fun'];
    supportKeywords.forEach(keyword => {
      const count = (allText.match(new RegExp(keyword, 'g')) || []).length;
      const density = totalWords > 0 ? (count / totalWords * 100) : 0;
      
      result.density[keyword] = { 
        count, 
        density: parseFloat(density.toFixed(2)),
        target: 2.0 // 辅助关键词目标密度
      };

      if (density > 0 && density <= 3) {
        result.score += 5;
      } else if (density > 3) {
        result.warnings.push(`Support keyword "${keyword}" used too frequently (${density.toFixed(2)}%)`);
      }
    });

    return result;
  }

  /**
   * 检查英文内容的可读性水平
   * @param {string} text 文本内容
   * @returns {number} 可读性分数 (0-100)
   */
  checkEnglishLevel(content, gameData) {
    const result = {
      score: 0,
      readabilityScore: 0,
      avgWordsPerSentence: 0,
      issues: [],
      suggestions: []
    };

    const allText = Object.values(content).join(' ');
    
    // 计算可读性分数
    result.readabilityScore = this.checkTextReadability(allText);
    
    // 检查句子长度
    const sentences = allText.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = allText.split(/\s+/).filter(w => w.length > 0);
    result.avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;

    // 评分
    if (result.readabilityScore >= this.qualityRules.englishLevel.requiredReadabilityScore) {
      result.score += 30;
    } else {
      result.issues.push(`Text too complex for 6th grade level (readability: ${result.readabilityScore.toFixed(1)})`);
    }

    if (result.avgWordsPerSentence <= this.qualityRules.englishLevel.maxWordsPerSentence) {
      result.score += 20;
    } else {
      result.issues.push(`Sentences too long (avg: ${result.avgWordsPerSentence.toFixed(1)} words, max: ${this.qualityRules.englishLevel.maxWordsPerSentence})`);
    }

    return result;
  }

  /**
   * 计算文本可读性分数 (简化版 Flesch Reading Ease)
   * @param {string} text 文本
   * @returns {number} 可读性分数
   */
  checkTextReadability(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // 简化的 Flesch Reading Ease 公式
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算单词音节数（简化版）
   * @param {string} word 单词
   * @returns {number} 音节数
   */
  countSyllables(word) {
    word = word.toLowerCase().replace(/[^a-z]/g, '');
    if (word.length === 0) return 0;
    
    const vowels = 'aeiouy';
    let syllableCount = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        syllableCount++;
      }
      previousWasVowel = isVowel;
    }

    // 处理以 'e' 结尾的单词
    if (word.endsWith('e') && syllableCount > 1) {
      syllableCount--;
    }

    return Math.max(1, syllableCount);
  }

  checkDuplicates(content) {
    const result = {
      score: 0,
      duplicates: [],
      issues: []
    };

    const sentences = [];
    Object.entries(content).forEach(([section, text]) => {
      const sectionSentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 10);
      sectionSentences.forEach(sentence => {
        sentences.push({ text: sentence.trim(), section });
      });
    });

    const sentenceMap = new Map();
    sentences.forEach(({ text, section }) => {
      if (sentenceMap.has(text)) {
        sentenceMap.get(text).push(section);
      } else {
        sentenceMap.set(text, [section]);
      }
    });

    sentenceMap.forEach((sections, text) => {
      if (sections.length > 1) {
        result.duplicates.push({
          text: text.substring(0, 50) + '...',
          sections: sections
        });
      }
    });

    if (result.duplicates.length === 0) {
      result.score = 100;
    } else {
      result.issues.push(`发现 ${result.duplicates.length} 处重复内容`);
      result.score = Math.max(0, 100 - result.duplicates.length * 20);
    }

    return result;
  }

  checkRequiredKeywords(text, keywords) {
    const result = {
      score: 0,
      issues: []
    };

    const missingKeywords = keywords.filter(keyword => !text.includes(keyword));
    
    if (missingKeywords.length === 0) {
      result.score = 40;
    } else {
      result.score = Math.max(0, 40 - missingKeywords.length * 10);
      result.issues.push(`缺少关键词: ${missingKeywords.join(', ')}`);
    }

    return result;
  }

  calculateOverallScore(details) {
    const weights = {
      title: 0.25,
      description: 0.25,
      content: 0.30,
      keywords: 0.10,
      englishLevel: 0.10,
      duplicates: 0.10
    };

    let totalScore = 0;
    Object.entries(weights).forEach(([key, weight]) => {
      if (details[key] && details[key].score !== undefined) {
        totalScore += details[key].score * weight;
      }
    });

    return Math.round(totalScore);
  }

  collectIssuesAndSuggestions(results) {
    Object.values(results.details).forEach(detail => {
      if (detail.issues) {
        results.issues.push(...detail.issues);
      }
      if (detail.suggestions) {
        results.suggestions.push(...detail.suggestions);
      }
    });
  }

  generateReport(qualityResult) {
    let report = '';
    
    const scoreColor = qualityResult.overall >= 80 ? '🟢' : 
                      qualityResult.overall >= 60 ? '🟡' : '🔴';
    report += `${scoreColor} 内容质量评分: ${qualityResult.overall}/100\n`;

    report += '\n📊 详细评分:\n';
    Object.entries(qualityResult.details).forEach(([key, detail]) => {
      const score = detail.score || 0;
      const emoji = score >= 80 ? '✅' : score >= 60 ? '⚠️' : '❌';
      report += `   ${emoji} ${this.getSectionName(key)}: ${Math.round(score)}/100\n`;
    });

    if (qualityResult.issues.length > 0) {
      report += '\n❌ 发现的问题:\n';
      qualityResult.issues.forEach(issue => {
        report += `   • ${issue}\n`;
      });
    }

    if (qualityResult.suggestions.length > 0) {
      report += '\n💡 改进建议:\n';
      qualityResult.suggestions.forEach(suggestion => {
        report += `   • ${suggestion}\n`;
      });
    }

    return report;
  }

  getSectionName(key) {
    const names = {
      title: '标题',
      description: '描述',
      content: '内容',
      keywords: '关键词',
      englishLevel: '英文可读性',
      duplicates: '重复检查',
      yamlSafety: 'YAML安全检查'
    };
    return names[key] || key;
  }

  /**
   * 检查YAML格式安全性
   * @param {Object} content 生成的内容对象
   * @returns {Object} 检查结果
   */
  checkYamlSafety(content) {
    const result = {
      score: 0,
      issues: [],
      warnings: []
    };

    const yamlFields = [
      { key: 'seoTitle', name: 'Title' },
      { key: 'metaDescription', name: 'Meta Description' },
      { key: 'pageDescription', name: 'Page Description' }
    ];

    let safeFieldsCount = 0;

    yamlFields.forEach(field => {
      const text = content[field.key];
      if (!text) return;

      // 检查未转义的双引号
      const innerQuotes = text.match(/(?<!^)"(?!$)/g);
      if (innerQuotes && innerQuotes.length > 0) {
        result.issues.push(`${field.name} contains unescaped quotes that may break YAML parsing`);
      } else {
        safeFieldsCount++;
      }

      // 检查YAML特殊字符
      const yamlSpecialChars = /[\r\n\t:{}\[\]|>]/;
      if (yamlSpecialChars.test(text)) {
        result.issues.push(`${field.name} contains special characters that may break YAML formatting`);
      } else {
        safeFieldsCount++;
      }

      // 检查过长的单行文本
      if (text.length > 500) {
        result.warnings.push(`${field.name} is very long (${text.length} chars), consider shortening`);
      }
    });

    // 计算安全分数
    const totalChecks = yamlFields.length * 2; // 每个字段检查2项
    result.score = Math.round((safeFieldsCount / totalChecks) * 100);

    return result;
  }
}

module.exports = ContentQualityChecker;
