"use client";

import React from 'react';
import Link from 'next/link';
import { ExternalLink, AlertTriangle } from 'lucide-react';
import { GameFrontmatter } from '@/lib/types';
import GameCard from './GameCard';

interface GameRecommendSectionProps {
  /** 当前游戏数据 */
  currentGame: GameFrontmatter;
  /** 推荐游戏列表 */
  recommendedGames: GameFrontmatter[];
  /** 是否显示错误状态 */
  showError?: boolean;
}

export default function GameRecommendSection({
  currentGame,
  recommendedGames,
  showError = false
}: GameRecommendSectionProps) {
  // 如果没有推荐游戏且不显示错误状态，不渲染组件
  if (recommendedGames.length === 0 && !showError) {
    return null;
  }

  return (
    <section className="w-full theme-card p-6">
      <div className="space-y-6">
        {/* 推荐游戏部分 */}
        {recommendedGames.length > 0 && (
          <div>
            <h2 className="text-xl font-bold text-foreground mb-4 flex items-center gap-2">
              <span>🎮</span>
              <span>You Might Also Like</span>
            </h2>
            
            {/* 游戏卡片网格 */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {recommendedGames.slice(0, 10).map((game) => (
                <GameCard
                  key={game.slug}
                  game={game}
                  size="small"
                  lazy={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* 游戏无法加载时的提示 */}
        {showError && (
          <div className="bg-muted rounded-lg p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Game Not Available
            </h3>
            <p className="theme-text-muted text-sm">
              This game is temporarily unavailable. Please try again later or explore our other games.
            </p>
            
            {/* 浏览更多游戏按钮 */}
            <Link
              href="/games"
              className="theme-btn theme-btn-primary px-4 py-2 gap-2 mt-4"
            >
              <span>Browse More Games</span>
              <ExternalLink className="w-4 h-4" />
            </Link>
          </div>
        )}

        {/* 游戏加载失败时的备用选项 */}
        {showError && recommendedGames.length === 0 && (
          <div className="bg-muted rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Unable to Load Game
            </h3>
            <p className="theme-text-muted">
              We're having trouble loading this game. Please check your connection and try again.
            </p>
            
            {/* 重试按钮 */}
            <button
              onClick={() => window.location.reload()}
              className="theme-btn theme-error px-4 py-2 gap-2 mt-4"
            >
              <span>Try Again</span>
            </button>
          </div>
        )}
      </div>
    </section>
  );
} 