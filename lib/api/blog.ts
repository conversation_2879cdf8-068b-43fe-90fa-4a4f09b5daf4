import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';

const blogsDirectory = path.join(process.cwd(), 'content/blog');

// Blog Frontmatter interface
export interface BlogFrontmatter {
  title: string;
  slug: string;
  thumbnail: string;
  publishedDate: string;
  author: string;
  description: string;
}

// Complete blog data structure
export interface Blog extends BlogFrontmatter {
  contentHtml: string;
}

/**
 * Get all blog slugs
 */
export function getBlogSlugs(): string[] {
  try {
    // Ensure directory exists
    if (!fs.existsSync(blogsDirectory)) {
      return [];
    }

    const fileNames = fs.readdirSync(blogsDirectory);
    return fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => fileName.replace(/\.md$/, ''));
  } catch (error) {
    return [];
  }
}

/**
 * Get complete blog data by slug
 */
export async function getBlogBySlug(slug: string): Promise<Blog | null> {
  try {
    const fullPath = path.join(blogsDirectory, `${slug}.md`);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return null;
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);

    // Validate required frontmatter fields
    const requiredFields = ['title', 'slug', 'thumbnail', 'publishedDate', 'author', 'description'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return null;
      }
    }

    // Process Markdown content using remark
    const remarkProcessed = await remark()
      .use(html)
      .process(content);
    const contentHtml = remarkProcessed.toString();

    const blog: Blog = {
      ...(data as BlogFrontmatter),
      contentHtml,
    };

    return blog;
  } catch (error) {
    return null;
  }
}

/**
 * Get all blogs' Frontmatter data
 */
export async function getAllBlogFrontmatters(options?: {
  sortByDate?: boolean;
}): Promise<BlogFrontmatter[]> {
  try {
    const slugs = getBlogSlugs();
    const blogs: BlogFrontmatter[] = [];

    for (const slug of slugs) {
      const fullPath = path.join(blogsDirectory, `${slug}.md`);
      
      try {
        const fileContents = fs.readFileSync(fullPath, 'utf8');
        const { data } = matter(fileContents);

        // Validate required fields
        const requiredFields = ['title', 'slug', 'thumbnail', 'publishedDate', 'author', 'description'];
        const isValid = requiredFields.every(field => data[field]);
        
        if (!isValid) {
          continue;
        }

        blogs.push(data as BlogFrontmatter);
      } catch (error) {
        continue;
      }
    }

    // Sort by date (newest first)
    if (options?.sortByDate) {
      blogs.sort((a, b) => {
        return new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime();
      });
    }

    return blogs;
  } catch (error) {
    return [];
  }
} 