require('dotenv').config({ path: '.env.local' });
const fs = require('fs').promises;
const path = require('path');
const { AI_CONFIG, CONTENT_CONFIG } = require('./config');
const Utils = require('./utils');
const AIService = require('./ai-service');
const BatchProcessor = require('./batch-processor');
const ContentQualityChecker = require('./content-quality');
const Logger = require('./logger');

/**
 * 增强版游戏页面生成器
 */
class EnhancedGameGenerator {
  constructor(options = {}) {
    this.logger = new Logger({
      logDir: 'scripts/logs',
      logLevel: options.logLevel || 'info'
    });

    this.dataFile = path.join(CONTENT_CONFIG.paths.dataDir, options.dataFile || 'new-games.json');
    this.templateFile = path.join(CONTENT_CONFIG.paths.templatesDir, 'game-template.md');
    this.outputDir = CONTENT_CONFIG.paths.outputDir;
    this.archiveDir = path.join(CONTENT_CONFIG.paths.dataDir, 'archive');
    
    this.useAI = options.useAI !== false;
    this.aiService = this.useAI ? new AIService() : null;
    
    this.batchProcessor = new BatchProcessor({
      batchSize: options.batchSize || 3,
      delayBetweenBatches: options.delayBetweenBatches || 2000,
      delayBetweenRequests: options.delayBetweenRequests || 500
    });
    
    this.qualityChecker = new ContentQualityChecker();
    
    this.options = {
      enableQualityCheck: options.enableQualityCheck !== false,
      minQualityScore: options.minQualityScore || 70,
      resumeOnFailure: options.resumeOnFailure !== false,
      generateReport: options.generateReport !== false,
      ...options
    };

    this.logger.info('Enhanced Game Generator initialized', {
      useAI: this.useAI,
      dataFile: this.dataFile,
      options: this.options
    });
  }

  async run() {
    const sessionId = this.logger.startOperation('GameGeneration', {
      useAI: this.useAI,
      dataFile: path.basename(this.dataFile)
    });

    try {
      this.logger.info('🚀 开始增强版游戏页面生成流程');
      
      await this.validateEnvironment();
      const games = await this.loadGameData();
      const template = await this.loadTemplate();
      
      const results = await this.batchProcessor.processBatch(
        games,
        async (game, index) => await this.processGame(game, index, template),
        { enableQualityCheck: this.options.enableQualityCheck }
      );
      
      if (this.options.generateReport) {
        await this.generateFinalReport(results, games);
      }
      
      if (this.useAI && this.aiService) {
        const aiStats = this.aiService.getUsageStats();
        this.logger.info('AI使用统计', aiStats);
      }
      
      this.logger.endOperation(sessionId, results);
      this.logger.endSession();
      
      return results;
      
    } catch (error) {
      this.logger.failOperation(sessionId, error);
      this.logger.error('游戏生成流程失败', error);
      this.logger.endSession();
      throw error;
    }
  }

  async validateEnvironment() {
    const validationId = this.logger.startOperation('EnvironmentValidation');
    
    try {
      this.logger.info('验证运行环境...');
      
      if (!Utils.fileExists(this.dataFile)) {
        throw new Error(`数据文件不存在: ${this.dataFile}`);
      }
      
      if (!Utils.fileExists(this.templateFile)) {
        throw new Error(`模板文件不存在: ${this.templateFile}`);
      }
      
      Utils.ensureDirectoryExists(this.outputDir);
      
      if (this.useAI && this.aiService) {
        await this.aiService.testConnection();
      }
      
      this.logger.endOperation(validationId, { status: 'success' });
      this.logger.info('✅ 环境验证通过');
      
    } catch (error) {
      this.logger.failOperation(validationId, error);
      throw error;
    }
  }

  async loadGameData() {
    const loadId = this.logger.startOperation('LoadGameData');
    
    try {
      this.logger.info('读取游戏数据...');
      const data = await fs.readFile(this.dataFile, 'utf8');
      const games = JSON.parse(data);
      
      if (!Array.isArray(games) || games.length === 0) {
        throw new Error('游戏数据必须是非空数组');
      }
      
      const validGames = [];
      for (let i = 0; i < games.length; i++) {
        const game = games[i];
        if (Utils.validateGameData(game)) {
          validGames.push(game);
        } else {
          this.logger.warn(`游戏数据验证失败，跳过: ${game.name || '未知游戏'}`);
        }
      }
      
      this.logger.endOperation(loadId, { 
        total: games.length, 
        valid: validGames.length,
        invalid: games.length - validGames.length
      });
      
      this.logger.info(`✅ 成功读取 ${validGames.length} 个有效游戏数据`);
      return validGames;
      
    } catch (error) {
      this.logger.failOperation(loadId, error);
      throw error;
    }
  }

  async loadTemplate() {
    const loadId = this.logger.startOperation('LoadTemplate');
    
    try {
      this.logger.info('读取模板文件...');
      const template = await fs.readFile(this.templateFile, 'utf8');
      
      this.logger.endOperation(loadId, { 
        templateSize: template.length,
        templateFile: path.basename(this.templateFile)
      });
      
      this.logger.info('✅ 模板文件读取成功');
      return template;
      
    } catch (error) {
      this.logger.failOperation(loadId, error);
      throw error;
    }
  }

  async processGame(game, index, template) {
    const gameId = this.logger.startOperation('ProcessGame', {
      gameName: game.name,
      index: index
    });

    try {
      const slug = Utils.generateSlug(game.name);
      const outputFile = path.join(this.outputDir, `${slug}.md`);
      
      if (Utils.fileExists(outputFile)) {
        this.logger.warn(`文件已存在，跳过: ${slug}.md`);
        this.logger.endOperation(gameId, { status: 'skipped', reason: 'file_exists' });
        return { status: 'skipped', game: game.name, reason: 'File already exists' };
      }
      
      const contentResult = await this.generateGameContent(game, slug, template);
      
      let qualityResult = null;
      if (this.options.enableQualityCheck) {
        qualityResult = this.qualityChecker.checkQuality(contentResult.content, game);
        
        this.logger.info(`质量评分: ${qualityResult.overall}/100`, {
          gameName: game.name,
          qualityScore: qualityResult.overall
        });
        
        if (qualityResult.overall < this.options.minQualityScore) {
          this.logger.warn(`质量评分低于要求 (${qualityResult.overall} < ${this.options.minQualityScore})`, {
            gameName: game.name,
            issues: qualityResult.issues
          });
        }
      }
      
      await fs.writeFile(outputFile, contentResult.finalContent, 'utf8');
      
      this.logger.endOperation(gameId, {
        status: 'success',
        outputFile: path.basename(outputFile),
        qualityScore: qualityResult?.overall,
        contentLength: contentResult.finalContent.length
      });
      
      // 自动添加到热门游戏配置
      try {
        const configAdded = Utils.addToHotGamesConfig(slug, {
          prepend: true, // 新游戏添加到顶部
          maxHotGames: 20 // 限制热门游戏数量
        });
        
        if (configAdded) {
          this.logger.info(`游戏已自动添加到热门游戏配置: ${slug}`);
        }
      } catch (configError) {
        this.logger.warn(`添加游戏到热门游戏配置失败: ${configError.message}`);
        // 不影响主流程，只记录警告
      }
      
      return {
        status: 'success',
        game: game.name,
        slug: slug,
        outputFile: outputFile,
        quality: qualityResult,
        aiGenerated: contentResult.aiGenerated,
        addedToHotGames: true
      };
      
    } catch (error) {
      this.logger.failOperation(gameId, error);
      throw error;
    }
  }

  async generateGameContent(game, slug, template) {
    if (this.useAI && this.aiService) {
      return await this.generateAIContent(game, slug, template);
    } else {
      return await this.generateBasicContent(game, slug, template);
    }
  }

  async generateAIContent(game, slug, template) {
    const aiId = this.logger.startOperation('AIGeneration', { gameName: game.name });
    
    try {
      const aiContent = await this.aiService.generateAllContent(game);
      const templateResult = await this.replaceTemplateContent(template, game, slug, aiContent, true);
      
      this.logger.endOperation(aiId, {
        status: 'success',
        contentSections: Object.keys(aiContent).length,
        linksAdded: templateResult.linksReport?.totalLinksAdded || 0
      });
      
      return {
        content: aiContent,
        finalContent: templateResult.content,
        linksReport: templateResult.linksReport,
        aiGenerated: true
      };
      
    } catch (error) {
      this.logger.failOperation(aiId, error);
      this.logger.warn(`AI生成失败，回退到基础模板: ${error.message}`);
      
      return await this.generateBasicContent(game, slug, template);
    }
  }

  async generateBasicContent(game, slug, template) {
    const basicId = this.logger.startOperation('BasicGeneration', { gameName: game.name });
    
    try {
      const basicContent = this.createBasicContent(game);
      const templateResult = await this.replaceTemplateContent(template, game, slug, basicContent, false);
      
      this.logger.endOperation(basicId, {
        status: 'success',
        contentType: 'basic',
        linksAdded: templateResult.linksReport?.totalLinksAdded || 0
      });
      
      return {
        content: basicContent,
        finalContent: templateResult.content,
        linksReport: templateResult.linksReport,
        aiGenerated: false
      };
      
    } catch (error) {
      this.logger.failOperation(basicId, error);
      throw error;
    }
  }

  createBasicContent(game) {
    const gameName = game.name;
    const gameType = game.genres[0];
    
    return {
      seoTitle: gameName,
      metaDescription: `Play ${gameName} free online! This ${gameType.toLowerCase()} game is fun and easy to play. No download needed - start playing now!`,
      pageDescription: `${gameName} is a fun ${gameType.toLowerCase()} game you can play online for free. This game is easy to learn and great for all players. You can start playing right away in your browser without any downloads required.`,
      introductionContent: `${gameName} is an exciting ${gameType.toLowerCase()} game that you can play online for free. This game offers engaging gameplay with intuitive controls, making it perfect for players of all skill levels. Start your adventure right away in your browser - no downloads or installations required!`,
      howToPlayContent: `Getting started with ${gameName} is simple:\n\n1. **Start the Game** - Click the play button to begin your adventure\n2. **Learn the Controls** - Use your mouse and keyboard to navigate and interact\n3. **Follow Instructions** - Pay attention to in-game tutorials and hints\n4. **Have Fun** - Enjoy the gameplay and challenge yourself to improve!`,
      featuresContent: this.generateSimplifiedFeatures(game)
    };
  }

  async replaceTemplateContent(template, game, slug, content, isAI) {
    const publishedDate = new Date().toISOString().split('T')[0];
    
    // 生成SEO关键词
    const seoKeywords = [
      game.name,
      ...game.genres,
      'free online game',
      'browser game',
      'no download'
    ].join(', ');

    const replacements = {
      '{{gameName}}': game.name,
      '{{seoTitle}}': game.name,
      '{{metaDescription}}': content.metaDescription,
      '{{pageDescription}}': content.pageDescription || content.metaDescription,
      '{{slug}}': slug,
      '{{publishedDate}}': publishedDate,
      '{{genreArray}}': JSON.stringify(game.genres),
      '{{tagsArray}}': JSON.stringify(game.tags || game.genres),
      '{{difficulty}}': game.difficulty || 'Medium',
      '{{playerCount}}': game.playerCount || 'Single Player',
      '{{iframeUrl}}': game.iframeUrl,
      '{{thumbnail}}': game.thumbnail || `/images/thumbnails/${slug}.png`,
      '{{seoKeywords}}': seoKeywords,
      '{{introductionContent}}': content.introductionContent,
      '{{featuresContent}}': content.featuresContent,
      '{{howToPlayContent}}': content.howToPlayContent
    };

    let result = template;
    for (const [placeholder, value] of Object.entries(replacements)) {
      result = result.replace(new RegExp(placeholder, 'g'), value);
    }

    return {
      content: result,
      linksReport: null
    };
  }

  generateSimplifiedFeatures(game) {
    const gameType = game.genres[0];
    const features = [
      '🎮 **Free to Play** - Enjoy this game at no cost',
      '🌐 **Browser Game** - Play instantly without downloads',
      '📱 **Easy to Learn** - Simple controls and intuitive gameplay'
    ];
    
    // 根据游戏类型添加特定特性
    if (game.genres.includes('RPG')) {
      features.push('⚔️ **Adventure Awaits** - Explore and discover new challenges');
    } else if (game.genres.includes('Platform')) {
      features.push('🏃 **Classic Action** - Jump, run and overcome obstacles');
    } else if (game.genres.includes('Action')) {
      features.push('💥 **Fast-Paced** - Quick reflexes and exciting gameplay');
    } else {
      features.push(`🎯 **${gameType} Fun** - Engaging ${gameType.toLowerCase()} experience`);
    }
    
    return features.join('\n');
  }

  async generateFinalReport(results, games) {
    const reportId = this.logger.startOperation('GenerateReport');
    
    try {
      const reportPath = path.join('scripts/logs', `report-${Date.now()}.txt`);
      let report = '';
      
      report += '🎮 游戏生成报告\n';
      report += '='.repeat(50) + '\n\n';
      report += `📊 总体统计:\n`;
      report += `   总游戏数: ${games.length}\n`;
      report += `   成功生成: ${results.success}\n`;
      report += `   失败数量: ${results.failed}\n`;
      report += `   处理时长: ${results.duration} 秒\n\n`;
      
      if (this.useAI && this.aiService) {
        const aiStats = this.aiService.getUsageStats();
        report += `🤖 AI使用统计:\n`;
        report += `   API请求数: ${aiStats.requestCount}\n`;
        report += `   总运行时间: ${aiStats.runtime}\n`;
        report += `   平均请求时间: ${aiStats.avgRequestTime}\n`;
        report += `   Token使用量: ${aiStats.totalTokens || '未知'}\n\n`;
      }
      
      if (results.failedGames && results.failedGames.length > 0) {
        report += `❌ 失败的游戏:\n`;
        results.failedGames.forEach(failed => {
          report += `   ${failed.index + 1}. ${failed.game.name}: ${failed.error}\n`;
        });
        report += '\n';
      }
      
      const sessionStats = this.logger.getSessionStats();
      report += `📈 会话统计:\n`;
      report += `   操作总数: ${sessionStats.operations.total}\n`;
      report += `   成功操作: ${sessionStats.operations.completed}\n`;
      report += `   失败操作: ${sessionStats.operations.failed}\n`;
      report += `   错误数量: ${sessionStats.errors}\n`;
      report += `   警告数量: ${sessionStats.warnings}\n`;
      
      await fs.writeFile(reportPath, report);
      
      this.logger.endOperation(reportId, { reportPath });
      this.logger.info(`📄 报告已生成: ${reportPath}`);
      
    } catch (error) {
      this.logger.failOperation(reportId, error);
      this.logger.error('生成报告失败', error);
    }
  }

  /**
   * 归档已处理的游戏数据
   * @param {Array} processedGames 已处理的游戏数组
   */
  async archiveProcessedGames(processedGames) {
    try {
      // 确保归档目录存在
      await fs.mkdir(this.archiveDir, { recursive: true });

      // 生成归档文件名（基于时间戳）
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const archiveFile = path.join(this.archiveDir, `processed-games-${timestamp}.json`);

      // 保存到归档文件
      await fs.writeFile(archiveFile, JSON.stringify(processedGames, null, 2), 'utf8');
      
      Utils.log(`✅ 已归档 ${processedGames.length} 个游戏到: ${archiveFile}`, 'success');
      
      return archiveFile;
    } catch (error) {
      Utils.log(`❌ 归档失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 从数据文件中移除已处理的游戏
   * @param {Array} processedGames 已处理的游戏数组
   */
  async removeProcessedGames(processedGames) {
    try {
      const processedNames = processedGames.map(game => game.name);
      
      // 读取当前数据文件
      const currentData = await this.loadGameData();
      
      // 过滤掉已处理的游戏
      const remainingGames = currentData.filter(game => !processedNames.includes(game.name));
      
      // 写回数据文件
      await fs.writeFile(this.dataFile, JSON.stringify(remainingGames, null, 2), 'utf8');
      
      Utils.log(`✅ 已从数据文件中移除 ${processedNames.length} 个已处理的游戏`, 'success');
      Utils.log(`📋 剩余待处理游戏: ${remainingGames.length} 个`, 'info');
      
    } catch (error) {
      Utils.log(`❌ 移除已处理游戏失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 处理所有游戏（带归档功能）
   * @param {Object} options 处理选项
   */
  async processAllGames(options = {}) {
    const { autoArchive = true, maxGames = null } = options;
    
    try {
      Utils.log('🚀 开始批量处理游戏...', 'info');

      const gameData = await this.loadGameData();
      
      if (gameData.length === 0) {
        Utils.log('📝 没有待处理的游戏数据', 'info');
        return { processed: [], archived: null };
      }

      // 限制处理数量
      const gamesToProcess = maxGames ? gameData.slice(0, maxGames) : gameData;
      
      Utils.log(`📋 找到 ${gameData.length} 个游戏，将处理 ${gamesToProcess.length} 个`, 'info');

      // 加载模板
      const template = await this.loadTemplate();
      
      const processedGames = [];
      const errors = [];

      // 逐个处理游戏
      for (const game of gamesToProcess) {
        try {
          Utils.log(`\n🎮 处理游戏: ${game.name}`, 'info');
          
          const result = await this.processGame(game, 0, template);
          processedGames.push({
            ...game,
            generatedFile: result.outputFile,
            processedAt: new Date().toISOString()
          });
          
          Utils.log(`✅ ${game.name} 处理完成`, 'success');
          
        } catch (error) {
          Utils.log(`❌ ${game.name} 处理失败: ${error.message}`, 'error');
          errors.push({ game: game.name, error: error.message });
        }
      }

      // 自动归档和清理
      let archiveFile = null;
      if (autoArchive && processedGames.length > 0) {
        Utils.log('\n📦 开始归档处理完成的游戏...', 'info');
        
        archiveFile = await this.archiveProcessedGames(processedGames);
        await this.removeProcessedGames(processedGames);
      }

      // 输出处理结果
      Utils.log('\n📊 处理结果汇总:', 'info');
      Utils.log(`✅ 成功处理: ${processedGames.length} 个游戏`, 'success');
      Utils.log(`❌ 处理失败: ${errors.length} 个游戏`, errors.length > 0 ? 'error' : 'info');
      
      if (errors.length > 0) {
        Utils.log('失败详情:', 'error');
        errors.forEach(err => Utils.log(`  - ${err.game}: ${err.error}`, 'error'));
      }

      if (archiveFile) {
        Utils.log(`📦 归档文件: ${archiveFile}`, 'info');
      }

      return {
        processed: processedGames,
        errors,
        archived: archiveFile
      };

    } catch (error) {
      Utils.log(`❌ 批量处理失败: ${error.message}`, 'error');
      throw error;
    }
  }
}

if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    useAI: !args.includes('--no-ai'),
    enableQualityCheck: !args.includes('--no-quality'),
    generateReport: !args.includes('--no-report'),
    logLevel: args.includes('--debug') ? 'debug' : 'info'
  };
  
  const dataFileArg = args.find(arg => arg.startsWith('--data='));
  if (dataFileArg) {
    options.dataFile = dataFileArg.split('=')[1];
  }
  
  const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
  if (batchSizeArg) {
    options.batchSize = parseInt(batchSizeArg.split('=')[1]);
  }
  
  console.log('🚀 启动增强版游戏生成器...');
  console.log(`🤖 AI生成: ${options.useAI ? '启用' : '禁用'}`);
  console.log(`🔍 质量检查: ${options.enableQualityCheck ? '启用' : '禁用'}`);
  console.log(`📄 生成报告: ${options.generateReport ? '启用' : '禁用'}`);
  
  const generator = new EnhancedGameGenerator(options);
  generator.run().catch(error => {
    console.error('💥 程序崩溃:', error.message);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = EnhancedGameGenerator;
