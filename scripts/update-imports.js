const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// 导入路径映射
const importPathMap = {
  // 组件路径更新
  '@/components/footer/Footer': '@/components/layout/footer/Footer',
  '@/components/header/Header': '@/components/layout/header/Header',
  '@/components/Sidebar': '@/components/layout/Sidebar',
  '@/components/MainContent': '@/components/layout/MainContent',
  '@/components/ErrorBoundary': '@/components/common/ErrorBoundary',
  '@/components/StructuredData': '@/components/common/StructuredData',
  '@/components/PerformanceMonitor': '@/components/common/PerformanceMonitor',
  '@/components/AdSenseAd': '@/components/common/ads/AdSenseAd',
  '@/components/BookmarkButton': '@/components/common/ui/BookmarkButton',
  '@/components/ThemeToggle': '@/components/common/ui/ThemeToggle',
  '@/components/NoSSR': '@/components/common/NoSSR',
  '@/components/GameCard': '@/components/features/game/GameCard',
  '@/components/GameContentSection': '@/components/features/game/GameContentSection',
  '@/components/GamePlayer': '@/components/features/game/GamePlayer',
  '@/components/GameRecommendSection': '@/components/features/game/GameRecommendSection',
  '@/components/RelatedGamesCarousel': '@/components/features/game/RelatedGamesCarousel',
  '@/components/ShareDropdown': '@/components/layout/ShareDropdown',
  
  // 库路径更新
  '@/lib/markdown': '@/lib/api/markdown',
  '@/lib/blog': '@/lib/api/blog',
  '@/lib/alt-text-utils': '@/lib/utils/alt-text-utils',
  '@/lib/utils': '@/lib/utils/utils',
  '@/lib/structured-data': '@/lib/constants/structured-data',
};

// 递归获取所有文件
async function getFiles(dir) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(subdirs.map(async (subdir) => {
    const res = path.resolve(dir, subdir);
    return (await stat(res)).isDirectory() ? getFiles(res) : res;
  }));
  return files.flat();
}

// 更新文件中的导入路径
async function updateImports(filePath) {
  try {
    // 只处理 .ts, .tsx, .js, .jsx 文件
    if (!['.ts', '.tsx', '.js', '.jsx'].includes(path.extname(filePath))) {
      return;
    }
    
    console.log(`Processing ${filePath}...`);
    
    let content = await readFile(filePath, 'utf8');
    let updated = false;
    
    // 替换导入路径
    for (const [oldPath, newPath] of Object.entries(importPathMap)) {
      const regex = new RegExp(`from\\s+['"]${oldPath}['"]`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, `from '${newPath}'`);
        updated = true;
      }
    }
    
    // 如果有更新，写入文件
    if (updated) {
      await writeFile(filePath, content, 'utf8');
      console.log(`Updated ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// 主函数
async function main() {
  try {
    // 获取所有文件
    const files = await getFiles('.');
    
    // 过滤掉 node_modules 和 .git 目录
    const filteredFiles = files.filter(file => 
      !file.includes('node_modules') && 
      !file.includes('.git') && 
      !file.includes('.next')
    );
    
    // 更新所有文件中的导入路径
    await Promise.all(filteredFiles.map(updateImports));
    
    console.log('Import paths updated successfully!');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();