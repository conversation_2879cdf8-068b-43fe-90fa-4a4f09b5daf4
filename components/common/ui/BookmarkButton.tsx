"use client";

import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import { GameFrontmatter } from '@/lib/types';

interface BookmarkButtonProps {
  /** 游戏数据 */
  game: GameFrontmatter;
  /** 自定义CSS类名 */
  className?: string;
  /** 按钮大小 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示文字 */
  showText?: boolean;
}

export default function BookmarkButton({ 
  game, 
  className = '', 
  size = 'medium',
  showText = false 
}: BookmarkButtonProps) {
  const { isFavorited, toggleFavorite } = useFavorites();
  const [showTooltip, setShowTooltip] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件已挂载到客户端
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 获取当前收藏状态
  const isCurrentlyFavorited = isMounted ? isFavorited(game.slug) : false;

  // 处理收藏切换
  const handleToggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isMounted || !game) return;
    
    // 添加动画效果
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 300);
    
    // 切换收藏状态
    const newFavoriteStatus = toggleFavorite(game);
    
    // 显示提示信息
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 2000);
  };

  // 获取按钮尺寸样式
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return 'p-1.5 w-7 h-7';
      case 'large':
        return 'p-3 w-12 h-12';
      default:
        return 'p-2 w-9 h-9';
    }
  };

  // 获取图标尺寸
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 'w-4 h-4';
      case 'large':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  // 如果还没有挂载到客户端，显示简单的收藏按钮
  if (!isMounted) {
    return (
      <div className={`relative ${className}`}>
        <button
          className={`${getSizeStyles()} hover:bg-muted rounded-full transition-colors text-muted-foreground cursor-not-allowed`}
          disabled
          aria-label="Add to favorites"
        >
          <Heart className={getIconSize()} />
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={handleToggleFavorite}
        className={`
          ${getSizeStyles()} 
          hover:bg-muted 
          rounded-full transition-all duration-200 
          ${isCurrentlyFavorited 
            ? 'text-destructive hover:text-destructive/80' 
            : 'text-muted-foreground hover:text-destructive'
          }
          ${isAnimating ? 'scale-110' : 'scale-100'}
          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-opacity-50
        `}
        title={isCurrentlyFavorited ? 'Remove from favorites' : 'Add to favorites'}
        aria-label={isCurrentlyFavorited ? 'Remove from favorites' : 'Add to favorites'}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <Heart 
          className={`
            ${getIconSize()} 
            transition-all duration-200
            ${isCurrentlyFavorited ? 'fill-current' : ''}
            ${isAnimating ? 'animate-pulse' : ''}
          `} 
        />
      </button>

      {/* 显示文字（可选） */}
      {showText && (
        <span className={`
          ml-2 text-sm font-medium
          ${isCurrentlyFavorited 
            ? 'text-destructive' 
            : 'text-muted-foreground'
          }
        `}>
          {isCurrentlyFavorited ? 'Favorited' : 'Add to Favorites'}
        </span>
      )}

      {/* 提示工具栏 */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-popover text-popover-foreground text-xs rounded-md whitespace-nowrap z-50 pointer-events-none border border-border shadow-md">
          {isCurrentlyFavorited ? '❤️ Added to favorites!' : '🤍 Add to favorites'}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div>
        </div>
      )}
    </div>
  );
}