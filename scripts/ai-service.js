require('dotenv').config({ path: '.env.local' });
const axios = require('axios');
const { AI_CONFIG } = require('./config');
const Utils = require('./utils');

/**
 * AI内容生成服务
 * 封装智谱AI GLM-4-Flash的调用逻辑和Prompt模板
 */
class AIService {
  constructor() {
    this.apiConfig = AI_CONFIG.ZHIPU;
    this.requestCount = 0;
    this.startTime = Date.now();
  }

  /**
   * 清理YAML字符串，确保安全用于frontmatter
   * @param {string} text 需要清理的文本
   * @returns {string} 清理后的安全文本
   */
  sanitizeYamlString(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }
    
    // 移除或转义可能导致YAML解析错误的字符
    return text
      // 移除内部的双引号，保留开头和结尾的引号结构
      .replace(/(?<!^)"(?!$)/g, '')
      // 移除可能的YAML特殊字符
      .replace(/[\r\n\t]/g, ' ')
      // 清理多余的空格
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 验证生成的内容是否包含可能导致YAML错误的字符
   * @param {string} content 内容
   * @returns {boolean} 是否安全
   */
  isYamlSafe(content) {
    if (!content || typeof content !== 'string') {
      return true;
    }
    
    // 检查是否包含未转义的双引号（除了开头和结尾）
    const innerQuotes = content.match(/(?<!^)"(?!$)/g);
    if (innerQuotes && innerQuotes.length > 0) {
      return false;
    }
    
    // 检查是否包含其他YAML特殊字符
    const yamlSpecialChars = /[\r\n\t:{}\[\]|>]/;
    if (yamlSpecialChars.test(content)) {
      return false;
    }
    
    return true;
  }

  /**
   * 调用AI生成内容的核心方法
   * @param {string} prompt 提示词
   * @param {Object} options 可选参数
   * @returns {Promise<string>} AI生成的内容
   */
  async generateContent(prompt, options = {}) {
    const {
      maxTokens = this.apiConfig.maxTokens,
      temperature = this.apiConfig.temperature,
      retryAttempts = this.apiConfig.retryAttempts
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        Utils.log(`AI请求 #${++this.requestCount} (尝试 ${attempt}/${retryAttempts})`, 'info');
        
        const response = await this.makeAPIRequest(prompt, {
          maxTokens,
          temperature
        });

        if (response && response.choices && response.choices[0]) {
          const content = response.choices[0].message.content;
          
          // 记录token使用情况
          if (response.usage) {
            Utils.log(`Token使用: ${response.usage.prompt_tokens} + ${response.usage.completion_tokens} = ${response.usage.total_tokens}`, 'info');
          }
          
          return content.trim();
        } else {
          throw new Error('API响应格式异常');
        }

      } catch (error) {
        lastError = error;
        Utils.log(`尝试 ${attempt} 失败: ${error.message}`, 'warning');
        
        if (attempt < retryAttempts) {
          const delay = this.apiConfig.retryDelay * attempt; // 递增延迟
          Utils.log(`等待 ${delay}ms 后重试...`, 'info');
          await Utils.delay(delay);
        }
      }
    }

    throw new Error(`AI生成失败，已重试${retryAttempts}次: ${lastError.message}`);
  }

  /**
   * 发送API请求
   * @param {string} prompt 提示词
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} API响应
   */
  async makeAPIRequest(prompt, options) {
    const requestData = {
      model: this.apiConfig.model,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: options.maxTokens,
      temperature: options.temperature
    };

    const response = await axios.post(
      this.apiConfig.baseURL,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${this.apiConfig.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.apiConfig.timeout
      }
    );

    return response.data;
  }

  /**
   * 生成SEO优化的标题 - 简化为直接使用游戏名称
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateSEOTitle(gameData) {
    // 直接返回游戏名称作为标题
    return gameData.name;
  }

  /**
   * 生成Meta描述
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateMetaDescription(gameData) {
    const prompt = `Write a meta description for this game in simple English (6th grade level):

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple English that 6th graders can understand
2. Length: 140-155 characters
3. Include the game name "${gameData.name}" exactly once
4. Include one game type from: ${gameData.genres.join(', ')}
5. Use words like "free", "online", "play" but keep keyword density under 4%
6. Make it exciting but simple
7. End with "Play now!"
8. IMPORTANT: Do NOT use quotation marks (") inside the text
9. IMPORTANT: Use only simple punctuation (periods, commas, exclamation marks)

Return only the meta description, no explanations. Avoid quotation marks completely.`;

    const content = await this.generateContent(prompt, { maxTokens: 100 });
    return this.sanitizeYamlString(content);
  }

  /**
   * 生成游戏介绍内容
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateIntroduction(gameData) {
    const prompt = `Write a game introduction in simple English:

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Start with a markdown H1 heading using EXACTLY: # ${gameData.name}
2. Write in simple, clear English suitable for all ages
3. Use short sentences (max 15 words each)
4. Write 2-3 short paragraphs (150-250 words total)
5. Include "${gameData.name}" exactly 2-3 times naturally
6. Include game type words naturally
7. Use words like "fun", "free", "online", "easy" moderately
8. Use neutral, professional but accessible language
9. Avoid age-specific references (no "Hey kids!", "children", etc.)
10. Focus on what makes the game enjoyable for all players
11. Make it engaging but maintain a professional tone

Write in Markdown format starting with the H1 heading. Use clear, accessible language that appeals to all age groups.`;

    return await this.generateContent(prompt, { maxTokens: 350 });
  }

  /**
   * 生成游戏特色功能描述
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateFeatures(gameData) {
    const prompt = `Write game features in simple English with improved structure:

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple, clear English suitable for all ages
2. Create 4-6 features
3. Format: ### emoji **Bold Title**
   Then write description on new line
4. Each description: 1-2 short sentences (max 15 words per sentence)
5. Include "${gameData.name}" once in the features naturally
6. Use simple words like: fun, easy, free, exciting, engaging
7. Use neutral, professional but accessible language
8. Avoid age-specific references (no "kids", "children", etc.)
9. Focus on features that appeal to all players
10. Add blank line between each feature for better spacing

Example format:
### 🎮 Easy to Play
Simple controls that anyone can learn quickly. Start playing right away!

### 🏆 Exciting Challenges
Beat levels and unlock new content as you progress through the game.

Write 4-6 features like this with proper heading structure and spacing.`;

    return await this.generateContent(prompt, { maxTokens: 300 });
  }

  /**
   * 生成游戏玩法说明
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateHowToPlay(gameData) {
    const prompt = `Write game instructions in simple English:

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple, clear English suitable for all ages
2. Create 4-8 numbered steps
3. Each step: 1-2 short sentences (max 15 words per sentence)
4. Include "${gameData.name}" once in the instructions naturally
5. Use action words: click, move, collect, win, play
6. Use neutral, professional but accessible language
7. Avoid age-specific references (no "kids", "children", etc.)
8. Make instructions clear and easy to follow for all players
9. Focus on the basic gameplay mechanics

Example format:
1. Click "Start Game" to begin playing.
2. Use arrow keys to move your character around.
3. Collect items to increase your score.

Write 4-8 clear steps that anyone can follow.`;

    return await this.generateContent(prompt, { maxTokens: 300 });
  }

  /**
   * 生成游戏控制说明
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateControls(gameData) {
    const gameType = gameData.genres[0].toLowerCase();
    
    const prompt = `Write game controls in simple English (6th grade level):

Game Information:
- Name: ${gameData.name}
- Type: ${gameType}

Requirements:
1. Write in simple English (6th grade level)
2. Use bullet points or simple list
3. Focus on basic controls for ${gameType} games
4. Use simple words: click, press, hold, move
5. Keep each line short (max 10 words)
6. Include mouse and keyboard controls
7. Make it easy to understand

Example format:
- **Arrow Keys** - Move left, right, up, down
- **Space Bar** - Jump or shoot
- **Mouse Click** - Select items

Write 4-6 simple controls like this.`;

    return await this.generateContent(prompt, { maxTokens: 200 });
  }

  /**
   * 生成游戏技巧和策略
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateTips(gameData) {
    const prompt = `Write game tips in simple English (6th grade level):

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple English (6th grade level):
   - Short sentences (max 12 words)
   - Simple words only
   - Clear and helpful advice
2. Write 4-6 tips
3. Use bullet points
4. Include "${gameData.name}" once
5. Use encouraging words: try, practice, remember
6. Give practical advice
7. Keep it simple and helpful

Example format:
- Practice the controls before starting hard levels.
- Look for hidden items in each area.
- Take breaks if the game gets too hard.

Write 4-6 simple tips like this.`;

    return await this.generateContent(prompt, { maxTokens: 250 });
  }

  /**
   * 生成相似游戏推荐
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateSimilarGames(gameData) {
    const prompt = `Write about similar games in simple English (6th grade level):

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}

Requirements:
1. Write in simple English (6th grade level)
2. Write 2-3 short sentences
3. Mention that we have other ${gameData.genres[0]} games
4. Use simple words: like, enjoy, try, play
5. Include "${gameData.name}" once
6. Keep it short and simple
7. Encourage trying other games

Example:
If you like ${gameData.name}, you will enjoy our other ${gameData.genres[0]} games. Try different games to find your favorite. We have many fun games to play!

Write 2-3 simple sentences like this.`;

    return await this.generateContent(prompt, { maxTokens: 150 });
  }

  /**
   * 生成页面详细描述
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generatePageDescription(gameData) {
    const prompt = `Write a concise page description for this game in simple English:

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple, clear English suitable for all ages
2. Length: MAXIMUM 200 words (keep it concise and summarized)
3. Include "${gameData.name}" exactly 2-3 times naturally
4. Include game type words naturally
5. Use words like "fun", "free", "online", "play" moderately
6. Focus on the core game features and what makes it enjoyable
7. Use neutral, professional but accessible language
8. Avoid age-specific references (no "kids", "children", etc.)
9. Make it informative and engaging for all players
10. End with an encouraging call to action
11. IMPORTANT: Do NOT use quotation marks (") anywhere in the text
12. IMPORTANT: Use only simple punctuation (periods, commas, exclamation marks)
13. IMPORTANT: Avoid special characters that might break YAML formatting

Write a concise, professional summary that appeals to all age groups. Keep it under 200 words and focus on summarizing the key game aspects. Avoid quotation marks completely.`;

    const content = await this.generateContent(prompt, { maxTokens: 300 });
    return this.sanitizeYamlString(content);
  }

  /**
   * 生成关于游戏的详细介绍
   * @param {Object} gameData 游戏数据
   * @returns {Promise<string>}
   */
  async generateAboutGame(gameData) {
    const prompt = `Write an "About the Game" section in simple English (6th grade level):

Game Information:
- Name: ${gameData.name}
- Type: ${gameData.genres.join(', ')}
- Description: ${gameData.gameDescription || gameData.shortDescription}

Requirements:
1. Write in simple English (6th grade level)
2. Write 2-3 short paragraphs (100-150 words total)
3. Include "${gameData.name}" exactly once
4. Explain what the game is about
5. Talk about who made it or what makes it special
6. Use simple words and short sentences
7. Make it interesting but easy to read

Write about the game's background, story, or what makes it unique. Keep it simple and fun.`;

    return await this.generateContent(prompt, { maxTokens: 250 });
  }

  /**
   * 生成所有内容的主方法
   * @param {Object} gameData 游戏数据
   * @returns {Promise<Object>}
   */
  async generateAllContent(gameData) {
    try {
      Utils.log(`开始为 ${gameData.name} 生成AI内容...`, 'info');

      const results = await Promise.all([
        this.generateSEOTitle(gameData),
        this.generateMetaDescription(gameData),
        this.generatePageDescription(gameData),
        this.generateIntroduction(gameData),
        this.generateAboutGame(gameData),
        this.generateFeatures(gameData),
        this.generateHowToPlay(gameData),
        this.generateControls(gameData),
        this.generateTips(gameData),
        this.generateSimilarGames(gameData)
      ]);

      const content = {
        seoTitle: results[0],
        metaDescription: results[1],
        pageDescription: results[2],
        introductionContent: results[3],
        aboutGameContent: results[4],
        featuresContent: results[5],
        howToPlayContent: results[6],
        controlsContent: results[7],
        tipsContent: results[8],
        similarGamesContent: results[9]
      };

      Utils.log(`✅ AI内容生成完成: ${gameData.name}`, 'success');
      return content;

    } catch (error) {
      Utils.log(`❌ AI内容生成失败: ${gameData.name}`, 'error');
      throw error;
    }
  }

  /**
   * 测试AI连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection() {
    try {
      Utils.log('测试AI连接...', 'info');
      const testPrompt = '请回复"连接成功"';
      const response = await this.generateContent(testPrompt, { maxTokens: 10 });
      
      if (response && response.length > 0) {
        Utils.log('✅ AI连接测试成功', 'success');
        return true;
      } else {
        throw new Error('AI响应为空');
      }
    } catch (error) {
      Utils.log(`❌ AI连接测试失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 获取API使用统计
   * @returns {Object} 使用统计信息
   */
  getUsageStats() {
    const runtime = Math.round((Date.now() - this.startTime) / 1000);
    return {
      requestCount: this.requestCount,
      runtime: `${runtime}秒`,
      avgRequestTime: this.requestCount > 0 ? `${Math.round(runtime / this.requestCount)}秒/请求` : '0秒/请求'
    };
  }
}

module.exports = AIService;
