const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

/**
 * YAML验证工具
 * 检查所有游戏文件的YAML frontmatter格式
 */
class YamlValidator {
  constructor() {
    this.gamesDirectory = path.join(process.cwd(), 'games');
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 检查单个文件的YAML格式
   * @param {string} filePath 文件路径
   * @returns {Object} 检查结果
   */
  validateFile(filePath) {
    const result = {
      file: path.basename(filePath),
      valid: true,
      errors: [],
      warnings: []
    };

    try {
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const parsed = matter(fileContents);
      
      // 检查必需字段
      const requiredFields = ['title', 'slug', 'thumbnail', 'genres', 'description', 'iframeUrl', 'publishedDate'];
      requiredFields.forEach(field => {
        if (!parsed.data[field]) {
          result.errors.push(`Missing required field: ${field}`);
          result.valid = false;
        }
      });

      // 检查YAML字符串中的特殊字符
      Object.entries(parsed.data).forEach(([key, value]) => {
        if (typeof value === 'string') {
          // 检查未转义的双引号
          const innerQuotes = value.match(/(?<!^)"(?!$)/g);
          if (innerQuotes && innerQuotes.length > 0) {
            result.errors.push(`Field "${key}" contains unescaped quotes: ${innerQuotes.length} found`);
            result.valid = false;
          }

          // 检查YAML特殊字符
          const yamlSpecialChars = /[\r\n\t:{}\[\]|>]/;
          if (yamlSpecialChars.test(value)) {
            result.warnings.push(`Field "${key}" contains special characters that might cause issues`);
          }

          // 检查过长字段
          if (value.length > 500) {
            result.warnings.push(`Field "${key}" is very long (${value.length} chars)`);
          }
        }
      });

      // 检查pageDescription字数
      if (parsed.data.pageDescription && parsed.data.pageDescription.length > 1000) {
        result.warnings.push(`pageDescription is too long (${parsed.data.pageDescription.length} chars, recommended: <500)`);
      }

    } catch (error) {
      result.valid = false;
      result.errors.push(`YAML parsing failed: ${error.message}`);
    }

    return result;
  }

  /**
   * 验证所有游戏文件
   * @returns {Object} 验证结果
   */
  validateAllFiles() {
    const results = {
      totalFiles: 0,
      validFiles: 0,
      invalidFiles: 0,
      files: []
    };

    if (!fs.existsSync(this.gamesDirectory)) {
      console.error('Games directory not found:', this.gamesDirectory);
      return results;
    }

    const files = fs.readdirSync(this.gamesDirectory)
      .filter(file => file.endsWith('.md'))
      .map(file => path.join(this.gamesDirectory, file));

    results.totalFiles = files.length;

    files.forEach(filePath => {
      const fileResult = this.validateFile(filePath);
      results.files.push(fileResult);
      
      if (fileResult.valid) {
        results.validFiles++;
      } else {
        results.invalidFiles++;
      }
    });

    return results;
  }

  /**
   * 生成验证报告
   * @param {Object} results 验证结果
   */
  generateReport(results) {
    console.log('\n🔍 YAML Validation Report');
    console.log('='.repeat(50));
    console.log(`📁 Total files: ${results.totalFiles}`);
    console.log(`✅ Valid files: ${results.validFiles}`);
    console.log(`❌ Invalid files: ${results.invalidFiles}`);
    console.log('');

    if (results.invalidFiles > 0) {
      console.log('❌ Files with errors:');
      results.files
        .filter(file => !file.valid)
        .forEach(file => {
          console.log(`\n📄 ${file.file}:`);
          file.errors.forEach(error => console.log(`   ❌ ${error}`));
          file.warnings.forEach(warning => console.log(`   ⚠️  ${warning}`));
        });
    }

    if (results.files.some(file => file.warnings.length > 0)) {
      console.log('\n⚠️  Files with warnings:');
      results.files
        .filter(file => file.warnings.length > 0 && file.valid)
        .forEach(file => {
          console.log(`\n📄 ${file.file}:`);
          file.warnings.forEach(warning => console.log(`   ⚠️  ${warning}`));
        });
    }

    if (results.validFiles === results.totalFiles) {
      console.log('\n🎉 All files passed YAML validation!');
    }
  }

  /**
   * 运行验证
   */
  run() {
    console.log('🚀 Starting YAML validation...');
    const results = this.validateAllFiles();
    this.generateReport(results);
    
    // 返回退出码
    return results.invalidFiles > 0 ? 1 : 0;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const validator = new YamlValidator();
  const exitCode = validator.run();
  process.exit(exitCode);
}

module.exports = YamlValidator; 