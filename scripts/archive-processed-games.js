#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const Utils = require('./utils');

/**
 * 游戏数据归档工具
 * 将已生成页面的游戏从new-games.json移动到归档文件
 */
class GameArchiver {
  constructor() {
    this.dataFile = 'scripts/data/new-games.json';
    this.gamesDir = 'games';
    this.archiveDir = 'scripts/data/archive';
  }

  async run() {
    console.log('🗃️  开始游戏数据归档流程...');
    
    try {
      // 确保归档目录存在
      Utils.ensureDirectoryExists(this.archiveDir);
      
      // 读取当前游戏数据
      const games = Utils.readJsonFile(this.dataFile);
      
      const processedGames = [];
      const unprocessedGames = [];
      
      // 检查每个游戏是否已生成页面
      for (const game of games) {
        const slug = Utils.generateSlug(game.name);
        const gameFile = path.join(this.gamesDir, `${slug}.md`);
        
        if (Utils.fileExists(gameFile)) {
          processedGames.push(game);
          console.log(`✅ 已处理: ${game.name}`);
        } else {
          unprocessedGames.push(game);
          console.log(`⏳ 未处理: ${game.name}`);
        }
      }
      
      if (processedGames.length > 0) {
        // 创建归档文件
        const timestamp = new Date().toISOString().split('T')[0];
        const archiveFile = path.join(this.archiveDir, `processed-${timestamp}.json`);
        
        // 保存已处理的游戏到归档
        fs.writeFileSync(archiveFile, JSON.stringify(processedGames, null, 2));
        console.log(`📦 已归档 ${processedGames.length} 个游戏到: ${archiveFile}`);
        
        // 更新new-games.json，只保留未处理的游戏
        fs.writeFileSync(this.dataFile, JSON.stringify(unprocessedGames, null, 2));
        console.log(`🔄 已更新 ${this.dataFile}，保留 ${unprocessedGames.length} 个未处理游戏`);
      } else {
        console.log('ℹ️  没有需要归档的游戏');
      }
      
      console.log('✅ 归档流程完成');
      
    } catch (error) {
      console.error('❌ 归档失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行归档工具
if (require.main === module) {
  const archiver = new GameArchiver();
  archiver.run();
}

module.exports = GameArchiver; 