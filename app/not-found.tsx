import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '404 - Page Not Found',
  description: 'The page you are looking for could not be found. Discover amazing games on our platform.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404 Icon */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-primary mb-4">
            404
          </div>
          <div className="text-6xl mb-4">🎮</div>
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold text-foreground mb-4">
          Game Not Found
        </h1>
        <p className="text-lg text-muted-foreground mb-8">
          Oops! The game or page you&apos;re looking for seems to have disappeared into the digital void.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link
            href="/"
            className="block w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            🏠 Back to Home
          </Link>
          
          <Link
            href="/new-games"
            className="block w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            🎯 Browse New Games
          </Link>
          
          <Link
            href="/hot-games"
            className="block w-full bg-accent hover:bg-accent/90 text-accent-foreground font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            🔥 Check Hot Games
          </Link>
        </div>

        {/* Help Text */}
        <div className="mt-8 text-sm text-muted-foreground">
          <p>If you think this is a mistake, please</p>
          <Link 
            href="/contact-us" 
            className="text-primary hover:underline"
          >
            contact us
          </Link>
        </div>
      </div>
    </div>
  );
} 