<!------------------------------------------------------------------------------------
1. Natural-language conversation with the user must be in Simplified Chinese.
2. When generating or modifying code:
-  All code content—including identifiers, keywords, strings, logs, and UI text—must be in English.
-  Insert comprehensive Chinese comments that explain key logic, parameters, return values, and edge cases.
3. Any explanations or instructions outside code blocks must also be in Chinese.
4. After receiving a request, briefly restate your understanding of the core requirement.  
-------------------------------------------------------------------------------------> 