"use client";

import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home, Gamepad2, Calendar, User, Tag } from 'lucide-react';
import { Game } from '@/lib/types';
import CollapsibleContent from '@/components/ui/CollapsibleContent';

// 面包屑导航项接口
interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

// 组件Props接口
interface GameContentSectionProps {
  /** 游戏数据 */
  game: Game;
  /** 自定义面包屑导航 */
  customBreadcrumbs?: BreadcrumbItem[];
  /** 是否显示游戏元信息 */
  showGameMeta?: boolean;
  /** 是否显示发布日期 */
  showPublishedDate?: boolean;
  /** 是否显示作者信息 */
  showAuthor?: boolean;
  /** 是否显示类型标签 */
  showGenres?: boolean;
  /** 自定义CSS类名 */
  className?: string;
  /** 内容区域自定义样式 */
  contentClassName?: string;
}

// 生成默认面包屑导航
const generateDefaultBreadcrumbs = (game: Game): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: 'Home',
      href: '/',
      icon: Home
    }
  ];

  // 如果游戏有类型，添加第一个类型作为分类页面
  if (game.genres && game.genres.length > 0) {
    const primaryGenre = game.genres[0];
    // 处理genres格式：直接使用原始的genre名称
    const genreLabel = primaryGenre;
    
    // 生成URL slug：移除空格，转换为小写，确保以"-games"结尾
    const genreSlug = primaryGenre.toLowerCase().includes('games')
      ? primaryGenre.toLowerCase().replace(/\s+/g, '-')
      : `${primaryGenre.toLowerCase().replace(/\s+/g, '-')}-games`;
    
    breadcrumbs.push({
      label: genreLabel,
      href: `/games/${genreSlug}`,
      icon: Gamepad2
    });
  }

  // 添加当前游戏页面（不可点击），使用slug作为显示文本
  breadcrumbs.push({
    label: game.slug
  });

  return breadcrumbs;
};

// 面包屑导航组件
const Breadcrumb: React.FC<{ items: BreadcrumbItem[] }> = ({ items }) => (
  <nav aria-label="Breadcrumb navigation" className="mb-6">
    <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        const Icon = item.icon;

        return (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 mx-2 text-muted-foreground" />
            )}
            
            {item.href && !isLast ? (
              <Link
                href={item.href}
                className="flex items-center space-x-1 hover:text-primary transition-colors"
              >
                {Icon && <Icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </Link>
            ) : (
              <span className={`flex items-center space-x-1 ${isLast ? 'text-foreground font-medium' : ''}`}>
                {Icon && <Icon className="w-4 h-4" />}
                <span>{item.label}</span>
              </span>
            )}
          </li>
        );
      })}
    </ol>
  </nav>
);

// 游戏元信息组件
const GameMetaInfo: React.FC<{
  game: Game;
  showPublishedDate?: boolean;
  showAuthor?: boolean;
  showGenres?: boolean;
}> = ({ game, showPublishedDate = false, showAuthor = true, showGenres = true }) => (
  <div className="flex flex-wrap items-center gap-4 mb-6 pb-6 border-b border-border">
    {/* 发布日期 - 默认隐藏，仅用于后台排序 */}
    {showPublishedDate && game.publishedDate && (
      <div className="flex items-center space-x-2 text-muted-foreground">
        <Calendar className="w-4 h-4" />
        <span className="text-sm">
          发布于 {new Date(game.publishedDate).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </span>
      </div>
    )}

    {/* 作者信息 */}
    {showAuthor && game.author && (
      <div className="flex items-center space-x-2 text-muted-foreground">
        <User className="w-4 h-4" />
        <span className="text-sm">作者：{game.author}</span>
      </div>
    )}

    {/* 版本信息 */}
    {game.version && (
      <div className="flex items-center space-x-2 text-muted-foreground">
        <Tag className="w-4 h-4" />
        <span className="text-sm">版本：{game.version}</span>
      </div>
    )}

    {/* 游戏类型标签 */}
    {showGenres && game.genres && game.genres.length > 0 && (
      <div className="flex items-center space-x-2">
        <Tag className="w-4 h-4 text-muted-foreground" />
        <div className="flex flex-wrap gap-2">
          {game.genres.map((genre) => {
            // 生成正确的URL slug：移除空格，转换为小写，确保以"-games"结尾但不重复
            const genreSlug = genre.toLowerCase().includes('games')
              ? genre.toLowerCase().replace(/\s+/g, '-')
              : `${genre.toLowerCase().replace(/\s+/g, '-')}-games`;
            
            return (
              <Link
                key={genre}
                href={`/games/${genreSlug}`}
                className="inline-block bg-primary/10 text-primary text-xs px-2 py-1 rounded-full font-medium hover:bg-primary/20 transition-colors"
              >
                {genre}
              </Link>
            );
          })}
        </div>
      </div>
    )}
  </div>
);

// 主组件
const GameContentSection: React.FC<GameContentSectionProps> = ({
  game,
  customBreadcrumbs,
  showGameMeta = true,
  showPublishedDate = false,
  showAuthor = true,
  showGenres = true,
  className = '',
  contentClassName = ''
}) => {
  // 生成面包屑导航
  const breadcrumbs = customBreadcrumbs || generateDefaultBreadcrumbs(game);

  // 错误处理
  if (!game) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">
            内容加载失败
          </h2>
          <p className="text-destructive/80">
            无法加载游戏内容，请稍后重试。
          </p>
        </div>
      </div>
    );
  }

  return (
    <section className={`p-6 sm:p-8 ${className}`} role="main" aria-label="游戏详情内容">
      {/* 面包屑导航 */}
      <Breadcrumb items={breadcrumbs} />

      {/* 移除游戏标题显示 - H1现在在iframe底部栏中显示 */}
      
      {/* 游戏页面描述 - 优先使用pageDescription，回退到description */}
      {(game.pageDescription || game.description) && (
        <div className="mb-6">
          <p className="text-lg text-foreground leading-relaxed">
            {game.pageDescription || game.description}
          </p>
        </div>
      )}

      {/* 游戏元信息 */}
      {showGameMeta && (
        <GameMetaInfo
          game={game}
          showPublishedDate={showPublishedDate}
          showAuthor={showAuthor}
          showGenres={showGenres}
        />
      )}

      {/* Markdown 内容 - 现在从H2开始，因为H1已被移除 */}
      {game.contentHtml && (
        <CollapsibleContent
          collapsedHeight={400}
          expandText="Show more"
          collapseText="Show less"
          className="mb-6"
          fadeHeight={80}
        >
          <article 
            className={`prose prose-lg dark:prose-invert max-w-none game-content ${contentClassName}`}
            role="article"
            aria-label="游戏详细介绍"
          >
            <div 
              dangerouslySetInnerHTML={{ __html: game.contentHtml }}
              className="
                text-foreground leading-relaxed
                [&>h2]:text-2xl [&>h2]:font-bold [&>h2]:text-foreground [&>h2]:mt-8 [&>h2]:mb-4 [&>h2]:border-b [&>h2]:border-border [&>h2]:pb-2
                [&>h4]:text-lg [&>h4]:font-medium [&>h4]:text-foreground [&>h4]:mt-5 [&>h4]:mb-2
                [&>ul]:mb-4 [&>ul]:pl-6 [&>ul>li]:mb-2 [&>ul>li]:text-foreground
                [&>ol]:mb-4 [&>ol]:pl-6 [&>ol>li]:mb-2 [&>ol>li]:text-foreground
                [&>blockquote]:border-l-4 [&>blockquote]:border-primary [&>blockquote]:pl-4 [&>blockquote]:py-2 [&>blockquote]:my-4 [&>blockquote]:bg-accent [&>blockquote]:italic
                [&>code]:bg-muted [&>code]:px-2 [&>code]:py-1 [&>code]:rounded [&>code]:text-sm [&>code]:font-mono
                [&>pre]:bg-muted [&>pre]:p-4 [&>pre]:rounded-lg [&>pre]:overflow-x-auto [&>pre]:my-4
                [&>img]:rounded-lg [&>img]:shadow-md [&>img]:my-4
                [&>table]:w-full [&>table]:border-collapse [&>table]:my-4
                [&>table>thead>tr>th]:border [&>table>thead>tr>th]:border-border [&>table>thead>tr>th]:bg-muted [&>table>thead>tr>th]:px-4 [&>table>thead>tr>th]:py-2 [&>table>thead>tr>th]:text-left [&>table>thead>tr>th]:font-semibold
                [&>table>tbody>tr>td]:border [&>table>tbody>tr>td]:border-border [&>table>tbody>tr>td]:px-4 [&>table>tbody>tr>td]:py-2
              "
            />
          </article>
        </CollapsibleContent>
      )}

      {/* 如果没有内容，显示提示 */}
      {!game.contentHtml && (
        <div className="text-center py-12 text-muted-foreground">
          <p>暂无详细介绍内容</p>
        </div>
      )}
    </section>
  );
};

export default GameContentSection;
export type { GameContentSectionProps, BreadcrumbItem };