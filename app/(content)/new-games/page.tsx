import GameCard from '@/components/features/game/GameCard';
import { getAllGameFrontmatters } from '@/lib/api/markdown';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'New Games',
  description: 'Discover the latest Pokemon games added to our platform. Browse through new releases and find your next gaming adventure.',
};

export default async function NewGamesPage() {
  // Get all games sorted by date (newest first)
  const games = await getAllGameFrontmatters({ sortByDate: true });

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-primary to-accent text-primary-foreground py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            New Games
          </h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Discover the latest games added to our platform. Fresh adventures await!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {games.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground">
                  All Games ({games.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-2">
                {games.map((game) => (
                  <GameCard key={game.slug} game={game} />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🎮</div>
              <h2 className="text-2xl font-bold text-foreground mb-4">
                No Games Found
              </h2>
              <p className="text-muted-foreground mb-8">
                We&apos;re working on adding new games. Check back soon!
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
} 