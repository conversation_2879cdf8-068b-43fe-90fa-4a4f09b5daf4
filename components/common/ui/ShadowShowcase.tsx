"use client";

import React from 'react';

interface ShadowShowcaseProps {
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 阴影展示组件，展示theme.css中定义的所有阴影变量
 */
export default function ShadowShowcase({ className = '' }: ShadowShowcaseProps) {
  // 阴影变量列表
  const shadows = [
    { name: 'shadow-2xs', variable: 'var(--shadow-2xs)' },
    { name: 'shadow-xs', variable: 'var(--shadow-xs)' },
    { name: 'shadow-sm', variable: 'var(--shadow-sm)' },
    { name: 'shadow (default)', variable: 'var(--shadow)' },
    { name: 'shadow-md', variable: 'var(--shadow-md)' },
    { name: 'shadow-lg', variable: 'var(--shadow-lg)' },
    { name: 'shadow-xl', variable: 'var(--shadow-xl)' },
    { name: 'shadow-2xl', variable: 'var(--shadow-2xl)' },
  ];

  return (
    <div className={`p-6 theme-card ${className}`}>
      <h3 className="text-lg font-medium mb-4">阴影变量展示</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {shadows.map((shadow, index) => (
          <div key={index} className="flex flex-col items-center">
            <div 
              className="w-24 h-24 bg-background rounded-md mb-2"
              style={{ boxShadow: shadow.variable }}
            />
            <div className="text-sm font-medium">{shadow.name}</div>
            <div className="text-xs text-muted-foreground">{shadow.variable}</div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 text-sm text-muted-foreground">
        此组件展示了theme.css中定义的所有阴影变量
      </div>
    </div>
  );
}