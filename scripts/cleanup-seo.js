#!/usr/bin/env node

/**
 * SEO清理脚本
 * 用于检查和清理可能导致Google Search Console出现意外URL的问题
 */

const fs = require('fs');
const path = require('path');

const Logger = require('./logger');
const logger = new Logger();

/**
 * 检查文件中是否包含占位符文本
 */
function checkForPlaceholders(filePath, content) {
  const placeholders = [
    'YOUR_GAME_',
    'YOUR_BANNER_AD_SLOT_ID',
    'YOUR_SIDEBAR_AD_SLOT_ID',
    'YOUR_IN_CONTENT_AD_SLOT_ID',
    'PLACEHOLDER_',
    'TODO:',
    'FIXME:',
  ];

  const issues = [];
  
  placeholders.forEach(placeholder => {
    if (content.includes(placeholder)) {
      issues.push(`发现占位符: ${placeholder}`);
    }
  });

  return issues;
}

/**
 * 扫描目录中的文件
 */
function scanDirectory(dirPath, extensions = ['.tsx', '.ts', '.jsx', '.js', '.md']) {
  const issues = [];
  
  if (!fs.existsSync(dirPath)) {
    return issues;
  }

  const files = fs.readdirSync(dirPath, { withFileTypes: true });
  
  files.forEach(file => {
    const fullPath = path.join(dirPath, file.name);
    
    if (file.isDirectory()) {
      // 跳过某些目录
      if (['node_modules', '.next', '.git', 'dist', 'build'].includes(file.name)) {
        return;
      }
      issues.push(...scanDirectory(fullPath, extensions));
    } else if (file.isFile()) {
      const ext = path.extname(file.name);
      if (extensions.includes(ext)) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          const fileIssues = checkForPlaceholders(fullPath, content);
          
          if (fileIssues.length > 0) {
            issues.push({
              file: fullPath,
              issues: fileIssues
            });
          }
        } catch (error) {
          logger.warn(`无法读取文件: ${fullPath} - ${error.message}`);
        }
      }
    }
  });

  return issues;
}

/**
 * 检查Next.js构建输出
 */
function checkBuildOutput() {
  const buildDir = path.join(process.cwd(), '.next');
  if (!fs.existsSync(buildDir)) {
    logger.warn('未找到.next构建目录，请先运行 npm run build');
    return [];
  }

  // 检查静态页面
  const staticDir = path.join(buildDir, 'static');
  const serverDir = path.join(buildDir, 'server');
  
  const issues = [];
  
  // 这里可以添加更多的构建输出检查逻辑
  
  return issues;
}

/**
 * 主函数
 */
async function main() {
  logger.info('开始SEO清理检查...');

  const projectRoot = process.cwd();
  
  // 检查关键目录
  const dirsToCheck = [
    'app',
    'components', 
    'lib',
    'config',
    'games',
    'blogs'
  ];

  let totalIssues = 0;

  for (const dir of dirsToCheck) {
    const dirPath = path.join(projectRoot, dir);
    logger.info(`检查目录: ${dir}`);
    
    const issues = scanDirectory(dirPath);
    
    if (issues.length > 0) {
      logger.warn(`在 ${dir} 目录中发现 ${issues.length} 个问题:`);
      issues.forEach(issue => {
        if (typeof issue === 'object') {
          logger.warn(`  文件: ${issue.file}`);
          issue.issues.forEach(desc => {
            logger.warn(`    - ${desc}`);
          });
        } else {
          logger.warn(`  - ${issue}`);
        }
      });
      totalIssues += issues.length;
    } else {
      logger.info(`  ✓ ${dir} 目录检查通过`);
    }
  }

  // 检查构建输出
  logger.info('检查构建输出...');
  const buildIssues = checkBuildOutput();
  if (buildIssues.length > 0) {
    logger.warn(`构建输出中发现 ${buildIssues.length} 个问题`);
    totalIssues += buildIssues.length;
  } else {
    logger.info('  ✓ 构建输出检查通过');
  }

  // 总结
  if (totalIssues === 0) {
    logger.info('✅ SEO检查完成，未发现问题');
  } else {
    logger.warn(`⚠️  SEO检查完成，发现 ${totalIssues} 个需要处理的问题`);
    
    logger.info('\n建议操作:');
    logger.info('1. 修复上述文件中的占位符文本');
    logger.info('2. 移除或重命名包含测试内容的文件');
    logger.info('3. 确保所有示例文件都在docs/目录下');
    logger.info('4. 重新构建项目: npm run build');
    logger.info('5. 检查生成的sitemap.xml: http://localhost:3000/sitemap.xml');
  }

  logger.info('\n其他SEO最佳实践:');
  logger.info('- 确保所有游戏文件都有完整的frontmatter');
  logger.info('- 检查robots.txt设置: http://localhost:3000/robots.txt');
  logger.info('- 在Google Search Console中提交更新的sitemap');
  logger.info('- 使用Google Search Console的URL检查工具验证修复效果');
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    logger.error('SEO清理检查失败:', error);
    process.exit(1);
  });
}

module.exports = { checkForPlaceholders, scanDirectory }; 