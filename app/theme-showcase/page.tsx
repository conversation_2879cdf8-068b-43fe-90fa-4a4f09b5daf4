"use client";

import React from 'react';
import ChartExample from '@/components/common/ui/ChartExample';
import ShadowShowcase from '@/components/common/ui/ShadowShowcase';
import RadiusShowcase from '@/components/common/ui/RadiusShowcase';
import EnhancedSidebar from '@/components/layout/EnhancedSidebar';
import GameCard from '@/components/features/game/GameCard';
import { GameFrontmatter } from '@/lib/types';

export default function ThemeShowcasePage() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  
  // 示例游戏数据
  const sampleGame: GameFrontmatter = {
    title: "Pokemon Demo Game",
    slug: "pokemon-demo",
    thumbnail: "/images/thumbnails/pokemon-gamma-emerald.png",
    genres: ["Pokemon", "RPG"],
    description: "A demo game showcasing theme variables",
    iframeUrl: "https://example.com/game",
    publishedDate: "2024-01-01",
    displayTitle: "Pokemon Theme Demo"
  };
  
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">主题系统完整展示</h1>
      
      <p className="text-lg mb-8">
        此页面展示了完整的主题化系统，包括所有组件如何响应 theme.css 中的变量。
      </p>
      
      <div className="space-y-12">
        {/* 新增：主题化工具类展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">主题化工具类</h2>
          
          {/* 状态颜色展示 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">状态颜色</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="theme-success p-4 rounded-lg text-center">
                <div className="font-medium">成功状态</div>
                <div className="text-sm opacity-90">.theme-success</div>
              </div>
              
              <div className="theme-warning p-4 rounded-lg text-center">
                <div className="font-medium">警告状态</div>
                <div className="text-sm opacity-90">.theme-warning</div>
              </div>
              
              <div className="theme-error p-4 rounded-lg text-center">
                <div className="font-medium">错误状态</div>
                <div className="text-sm opacity-90">.theme-error</div>
              </div>
              
              <div className="theme-info p-4 rounded-lg text-center">
                <div className="font-medium">信息状态</div>
                <div className="text-sm opacity-90">.theme-info</div>
              </div>
            </div>
          </div>

          {/* 按钮系统展示 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">主题化按钮</h3>
            <div className="flex flex-wrap gap-4">
              <button className="theme-btn theme-btn-primary px-4 py-2">
                主要按钮
              </button>
              
              <button className="theme-btn theme-btn-secondary px-4 py-2">
                次要按钮
              </button>
              
              <button className="theme-btn theme-btn-outline px-4 py-2">
                轮廓按钮
              </button>
              
              <button className="theme-btn theme-error px-4 py-2">
                危险按钮
              </button>
            </div>
          </div>

          {/* 标签和徽章展示 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">标签和徽章</h3>
            <div className="flex flex-wrap gap-3">
              <span className="theme-badge theme-badge-hot">
                🔥 HOT
              </span>
              
              <span className="theme-badge theme-badge-new">
                ✨ NEW
              </span>
              
              <span className="theme-badge theme-badge-featured">
                ⭐ FEATURED
              </span>
            </div>
          </div>

          {/* 卡片系统展示 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">卡片系统</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="theme-card p-4">
                <h4 className="font-medium mb-2">基础卡片</h4>
                <p className="theme-text-muted text-sm">使用 .theme-card 类的基础卡片样式</p>
              </div>
              
              <div className="theme-card-elevated p-4">
                <h4 className="font-medium mb-2">提升卡片</h4>
                <p className="theme-text-muted text-sm">使用 .theme-card-elevated 类的提升卡片样式</p>
              </div>
            </div>
          </div>

          {/* 交互状态展示 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">交互状态</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="theme-hover p-4 border border-border rounded-lg cursor-pointer">
                <div className="font-medium">悬停效果</div>
                <div className="theme-text-muted text-sm">鼠标悬停查看效果</div>
              </div>
              
              <div className="theme-skeleton p-4 rounded-lg">
                <div className="font-medium">骨架屏样式</div>
                <div className="text-sm opacity-75">加载状态展示</div>
              </div>
              
              <div className="theme-skeleton-dark p-4 rounded-lg">
                <div className="font-medium">深色骨架屏</div>
                <div className="text-sm opacity-75">深色加载状态</div>
              </div>
            </div>
          </div>
        </section>

        {/* 新增：游戏组件展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">游戏组件展示</h2>
          
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">游戏卡片（不同尺寸）</h3>
            <div className="flex flex-wrap gap-4 items-end">
              <div className="text-center">
                <div className="mb-2 text-sm theme-text-muted">Small</div>
                <GameCard 
                  game={sampleGame} 
                  size="small" 
                  tags={['hot']}
                  lazy={false}
                />
              </div>
              
              <div className="text-center">
                <div className="mb-2 text-sm theme-text-muted">Medium</div>
                <GameCard 
                  game={sampleGame} 
                  size="medium" 
                  tags={['new']}
                  lazy={false}
                />
              </div>
              
              <div className="text-center">
                <div className="mb-2 text-sm theme-text-muted">Large</div>
                <GameCard 
                  game={sampleGame} 
                  size="large" 
                  tags={['top_rated']}
                  lazy={false}
                />
              </div>
            </div>
          </div>
        </section>

        {/* 颜色变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">核心颜色变量</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-background border border-border rounded-lg">
              <div className="font-medium">背景色 (--background)</div>
              <div className="text-sm text-muted-foreground">用于页面背景</div>
            </div>
            
            <div className="p-4 bg-card border border-border rounded-lg">
              <div className="font-medium">卡片色 (--card)</div>
              <div className="text-sm text-card-foreground">用于卡片背景</div>
            </div>
            
            <div className="p-4 bg-primary text-primary-foreground rounded-lg">
              <div className="font-medium">主色 (--primary)</div>
              <div className="text-sm">用于主要按钮和强调元素</div>
            </div>
            
            <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
              <div className="font-medium">次色 (--secondary)</div>
              <div className="text-sm">用于次要按钮和元素</div>
            </div>
            
            <div className="p-4 bg-accent text-accent-foreground rounded-lg">
              <div className="font-medium">强调色 (--accent)</div>
              <div className="text-sm">用于强调元素</div>
            </div>
            
            <div className="p-4 bg-muted rounded-lg">
              <div className="font-medium">柔和色 (--muted)</div>
              <div className="text-sm text-muted-foreground">用于柔和背景</div>
            </div>
            
            <div className="p-4 bg-popover rounded-lg">
              <div className="font-medium">弹出色 (--popover)</div>
              <div className="text-sm text-popover-foreground">用于弹出框背景</div>
            </div>
            
            <div className="p-4 bg-destructive text-destructive-foreground rounded-lg">
              <div className="font-medium">危险色 (--destructive)</div>
              <div className="text-sm">用于危险操作和错误提示</div>
            </div>
          </div>
        </section>
        
        {/* 图表颜色变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">图表颜色变量</h2>
          <ChartExample className="max-w-2xl" />
        </section>
        
        {/* 阴影变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">阴影变量</h2>
          <ShadowShowcase />
        </section>
        
        {/* 圆角变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">圆角变量</h2>
          <RadiusShowcase />
        </section>
        
        {/* 侧边栏变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">侧边栏变量</h2>
          <div className="relative h-96 border border-border rounded-lg overflow-hidden">
            <EnhancedSidebar 
              isOpen={sidebarOpen} 
              onToggle={() => setSidebarOpen(!sidebarOpen)} 
            />
            
            <div className="p-4 ml-0 md:ml-64">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="md:hidden mb-4 theme-btn theme-btn-primary px-4 py-2"
              >
                {sidebarOpen ? '关闭侧边栏' : '打开侧边栏'}
              </button>
              
              <div className="theme-card p-4">
                <h3 className="text-lg font-medium mb-2">侧边栏变量展示</h3>
                <p className="text-sm theme-text-muted">
                  此组件使用了theme.css中定义的所有侧边栏相关变量，包括：
                </p>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>--sidebar</li>
                  <li>--sidebar-foreground</li>
                  <li>--sidebar-border</li>
                  <li>--sidebar-primary</li>
                  <li>--sidebar-primary-foreground</li>
                  <li>--sidebar-accent</li>
                  <li>--sidebar-accent-foreground</li>
                  <li>--sidebar-ring</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
        
        {/* 字体变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">字体变量</h2>
          
          <div className="space-y-4">
            <div className="theme-card p-4">
              <div className="font-medium mb-2">Sans 字体 (--font-sans)</div>
              <p style={{ fontFamily: 'var(--font-sans)' }} className="text-lg">
                The quick brown fox jumps over the lazy dog. 0123456789
              </p>
            </div>
            
            <div className="theme-card p-4">
              <div className="font-medium mb-2">Serif 字体 (--font-serif)</div>
              <p style={{ fontFamily: 'var(--font-serif)' }} className="text-lg">
                The quick brown fox jumps over the lazy dog. 0123456789
              </p>
            </div>
            
            <div className="theme-card p-4">
              <div className="font-medium mb-2">Mono 字体 (--font-mono)</div>
              <p style={{ fontFamily: 'var(--font-mono)' }} className="text-lg">
                The quick brown fox jumps over the lazy dog. 0123456789
              </p>
            </div>
          </div>
        </section>
        
        {/* 间距变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">间距变量</h2>
          
          <div className="theme-card p-4">
            <div className="font-medium mb-2">基础间距 (--spacing)</div>
            <div className="flex items-center gap-4">
              <div className="bg-primary" style={{ width: 'var(--spacing)', height: 'var(--spacing)' }}></div>
              <span className="text-sm theme-text-muted">0.25rem</span>
            </div>
          </div>
        </section>
        
        {/* 字母间距变量展示 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">字母间距变量</h2>
          
          <div className="theme-card p-4">
            <div className="font-medium mb-2">正常字母间距 (--tracking-normal)</div>
            <p style={{ letterSpacing: 'var(--tracking-normal)' }} className="text-lg">
              The quick brown fox jumps over the lazy dog. 0123456789
            </p>
          </div>
        </section>

        {/* 新增：主题切换测试 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">主题响应性测试</h2>
          
          <div className="theme-card p-6">
            <h3 className="text-lg font-medium mb-4">切换主题测试所有组件</h3>
            <p className="theme-text-muted mb-4">
              使用页面右上角的主题切换按钮来测试所有组件如何响应主题变化。
              所有组件都应该无缝切换到新的颜色方案。
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">应该响应主题的元素：</h4>
                <ul className="text-sm theme-text-muted space-y-1">
                  <li>✅ 所有按钮和交互元素</li>
                  <li>✅ 卡片和容器背景</li>
                  <li>✅ 文本颜色和边框</li>
                  <li>✅ 游戏卡片和标签</li>
                  <li>✅ 阴影和圆角效果</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">测试步骤：</h4>
                <ol className="text-sm theme-text-muted space-y-1">
                  <li>1. 切换到深色主题</li>
                  <li>2. 检查所有组件颜色</li>
                  <li>3. 切换回浅色主题</li>
                  <li>4. 验证组件响应正常</li>
                </ol>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}