@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入核心主题变量 - 确保在基础层之后导入 */
@import './theme.css'; 

/* 导入游戏卡片动画样式 */
@import './game-card-animations.css';

/* 自定义滚动条样式 - 适配主题变量 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: calc(var(--radius) - 2px);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: calc(var(--radius) - 2px);
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

::-webkit-scrollbar-corner {
  background: var(--muted);
}

/* Firefox 滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--muted-foreground) var(--muted);
}

.sidebar-content {
  height: 100%;
  background: var(--sidebar);
  border-radius: var(--radius);
  border: 1px solid var(--sidebar-border);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--sidebar-foreground);
  border-bottom: 1px solid var(--sidebar-border);
  background: var(--muted);
  flex-shrink: 0;
}

/* 移动端优化 */
@media (max-width: 640px) {
  .game-title {
    font-size: 1rem;
  }
  
  .button-section {
    gap: 0.5rem;
  }
  
  .control-button {
    padding: 0.375rem;
  }
}

/* Responsive improvements - 移动端优化 */
@media (max-width: 767px) {
  .sidebar-title {
    font-size: 0.875rem;
    padding: 0.75rem;
  }
  
  .sidebar-games {
    padding: 0.25rem;
  }
}

html {
  scroll-behavior: smooth;
}


@layer base {
  * {
    border-color: var(--border);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-sans);
  }
}

/* 移动端优化样式 */
@layer utilities {
  /* === 基于主题变量的语义化工具类 === */
  
  /* 状态颜色类 - 基于现有主题变量 */
  .theme-success {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
  }
  
  .theme-warning {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }
  
  .theme-error {
    background-color: var(--destructive);
    color: var(--destructive-foreground);
  }
  
  .theme-info {
    background-color: var(--primary);
    color: var(--primary-foreground);
  }
  
  /* 状态颜色的hover变体 */
  .theme-success:hover {
    background-color: var(--secondary);
    opacity: 0.9;
  }
  
  .theme-warning:hover {
    background-color: var(--accent);
    opacity: 0.9;
  }
  
  .theme-error:hover {
    background-color: var(--destructive);
    opacity: 0.9;
  }
  
  .theme-info:hover {
    background-color: var(--primary);
    opacity: 0.9;
  }
  
  /* 骨架屏和加载状态 */
  .theme-skeleton {
    background-color: var(--muted);
    color: var(--muted-foreground);
  }
  
  .theme-skeleton-dark {
    background-color: var(--card);
    color: var(--card-foreground);
  }
  
  /* 交互状态类 */
  .theme-hover {
    transition: all 0.2s ease-in-out;
  }
  
  .theme-hover:hover {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }
  
  /* 卡片和容器类 */
  .theme-card {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
  }
  
  .theme-card-elevated {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
  }
  
  /* 按钮基础类 */
  .theme-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: 1px solid transparent;
  }
  
  .theme-btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
  }
  
  .theme-btn-primary:hover {
    opacity: 0.9;
    box-shadow: var(--shadow-md);
  }
  
  .theme-btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border-color: var(--secondary);
  }
  
  .theme-btn-secondary:hover {
    opacity: 0.9;
    box-shadow: var(--shadow-md);
  }
  
  .theme-btn-outline {
    background-color: transparent;
    color: var(--foreground);
    border-color: var(--border);
  }
  
  .theme-btn-outline:hover {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }
  
  /* 标签和徽章类 */
  .theme-badge {
    display: inline-flex;
    align-items: center;
    border-radius: calc(var(--radius) - 2px);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
  }
  
  .theme-badge-hot {
    background-color: var(--destructive);
    color: var(--destructive-foreground);
  }
  
  .theme-badge-new {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
  }
  
  .theme-badge-featured {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }
  
  /* 文本颜色类 */
  .theme-text-muted {
    color: var(--muted-foreground);
  }
  
  .theme-text-emphasis {
    color: var(--foreground);
    font-weight: 600;
  }
  
  /* 边框和分割线 */
  .theme-border {
    border-color: var(--border);
  }
  
  .theme-divider {
    height: 1px;
    background-color: var(--border);
    border: none;
  }

  /* 防止移动端水平滚动 */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* 移动端触摸优化 */
  .touch-action-pan-y {
    touch-action: pan-y;
  }

  /* 移动端侧边栏动画优化 */
  .sidebar-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }

  /* 移动端遮罩层渐变效果 */
  .mobile-overlay {
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
  }

  .mobile-overlay.visible {
    opacity: 1;
  }

  /* 移动端安全区域适配 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端点击高亮优化 */
  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }

  /* 移动端滚动优化 */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 移动端字体大小优化 */
  @media (max-width: 640px) {
    .text-responsive-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    
    .text-responsive-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }
    
    .text-responsive-lg {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  /* 移动端按钮优化 */
  @media (max-width: 640px) {
    .btn-mobile {
      min-height: 44px;
      min-width: 44px;
      padding: 0.75rem 1rem;
    }
  }

  /* 移动端卡片间距优化 */
  @media (max-width: 640px) {
    .card-mobile {
      margin: 0.5rem;
      padding: 1rem;
      box-shadow: var(--shadow);
    }
  }
}


/* Game content formatting improvements */
.game-content h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(to right, var(--muted), var(--accent));
  padding: 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

.game-content h3 + p {
  margin-top: 0.75rem;
  margin-bottom: 1rem;
  color: var(--muted-foreground);
  line-height: 1.625;
  padding-left: var(--spacing);
}

/* Emoji styling in headings */
.game-content h3:first-child {
  font-size: 1.5rem;
  line-height: 2rem;
  letter-spacing: var(--tracking-normal);
}

/* Better spacing for game features section */
.game-content h2:has-text('Game Features') ~ h3 {
  background: linear-gradient(to right, var(--primary), var(--accent));
  border-color: var(--primary);
}

/* Improve readability of feature descriptions */
.game-content h3 + p {
  font-size: 1rem;
  line-height: 1.75rem;
  color: var(--foreground);
}

/* Better list formatting */
.game-content ul li,
.game-content ol li {
  margin-bottom: 0.75rem;
  line-height: 1.625;
  color: var(--foreground);
}

/* Improved paragraph spacing */
.game-content p {
  margin-bottom: 1rem;
  line-height: 1.75rem;
  color: var(--foreground);
}