import GamePlayer from '@/components/features/game/GamePlayer';
import GameContentSection from '@/components/features/game/GameContentSection';
import RelatedGamesCarousel from '@/components/features/game/RelatedGamesCarousel';

import StructuredData from '@/components/common/StructuredData';
import { getGameBySlug, getRelatedGameFrontmatters, getAllGameFrontmatters, getHotGameFrontmatters } from '@/lib/api/markdown';
import { generateVideoGameSchema, generateWebSiteSchema, generateOrganizationSchema, combineStructuredData } from '@/lib/constants/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// 硬编码指定的核心游戏slug
const CORE_GAME_SLUG = 'pokemon-gamma-emerald';

/**
 * 智能合并游戏列表的函数
 * 将最新游戏和热门游戏智能穿插，生成统一的侧边栏推荐列表
 * 
 * @param newGames - 最新游戏列表（按日期排序）
 * @param hotGames - 热门游戏列表（按站长配置的顺序）
 * @param coreGameSlug - 核心游戏slug，需要排除
 * @returns 长度为8的智能合并游戏列表
 */
function createSmartGameList(newGames: any[], hotGames: any[], coreGameSlug: string) {
  // 1. 数据清理：从新游戏列表中移除核心游戏和热门游戏中已存在的游戏
  const hotGameSlugs = hotGames.map(game => game.slug);
  const filteredNewGames = newGames.filter(game => 
    game.slug !== coreGameSlug && !hotGameSlugs.includes(game.slug)
  );
  
  // 2. 智能穿插算法：创建最终推荐列表，目标24个游戏
  const finalList = [];
  const usedSlugs = new Set(); // 用于跟踪已添加的游戏，避免重复
  let newGameIndex = 0;
  let hotGameIndex = 0;
  
  // 穿插策略：每3个位置中，前2个是新游戏，第3个是热门游戏
  // 这样既保证了新游戏的曝光度，又突出了热门游戏
  for (let i = 0; i < 24; i++) { // 从28改为24个游戏
    let gameToAdd = null;
    
    if (i % 3 === 2 && hotGameIndex < hotGames.length) {
      // 每第3个位置（索引2,5,8...）插入热门游戏
      while (hotGameIndex < hotGames.length) {
        const game = hotGames[hotGameIndex];
        if (!usedSlugs.has(game.slug)) {
          gameToAdd = game;
          break;
        }
        hotGameIndex++;
      }
      hotGameIndex++;
    } else if (newGameIndex < filteredNewGames.length) {
      // 其他位置插入新游戏
      while (newGameIndex < filteredNewGames.length) {
        const game = filteredNewGames[newGameIndex];
        if (!usedSlugs.has(game.slug)) {
          gameToAdd = game;
          break;
        }
        newGameIndex++;
      }
      newGameIndex++;
    } else if (hotGameIndex < hotGames.length) {
      // 如果新游戏用完了，继续用热门游戏填充
      while (hotGameIndex < hotGames.length) {
        const game = hotGames[hotGameIndex];
        if (!usedSlugs.has(game.slug)) {
          gameToAdd = game;
          break;
        }
        hotGameIndex++;
      }
      hotGameIndex++;
    }
    
    if (gameToAdd && !usedSlugs.has(gameToAdd.slug)) {
      finalList.push(gameToAdd);
      usedSlugs.add(gameToAdd.slug);
    }
    
    // 如果所有游戏都用完了，提前结束
    if (finalList.length >= 24 || (newGameIndex >= filteredNewGames.length && hotGameIndex >= hotGames.length)) {
      break;
    }
  }
  
  return finalList;
}

// Generate dynamic metadata for SEO optimization
export async function generateMetadata(): Promise<Metadata> {
  const game = await getGameBySlug(CORE_GAME_SLUG);
  
  if (!game) {
    return {
      title: 'Play Free Pokemon Games',
      description: 'Discover and play amazing free online Pokemon games on our platform.',
    };
  }

  // Use the game's metadata for optimal SEO - keep title simple
  const pageTitle = game.title;
  const pageDescription = game.description;

  return {
    title: pageTitle,
    description: pageDescription,
    authors: game.author ? [{ name: game.author }] : [{ name: 'pokemon-gamma-emerald.com' }],
    openGraph: {
      title: game.title,
      description: pageDescription,
      type: 'website',
      url: '/',
      images: [
        {
          url: game.thumbnail,
          width: 1200,
          height: 630,
          alt: game.title,
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
    },
    twitter: {
      card: 'summary_large_image',
      title: game.title,
      description: pageDescription,
      images: [game.thumbnail],
    },
    alternates: {
      canonical: '/',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

// 生成首页结构化数据
function generateHomePageStructuredData(game: any) {
  const gameSchema = generateVideoGameSchema({
    title: game.title,
    slug: game.slug,
    description: game.description,
    thumbnail: game.thumbnail,
    genres: game.genres,
    publishedDate: game.publishedDate,
    author: game.author,
    version: game.version,
    iframeUrl: game.iframeUrl,
    pageDescription: game.pageDescription,
  }, siteConfig.url);

  const websiteSchema = generateWebSiteSchema();
  const organizationSchema = generateOrganizationSchema();

  return combineStructuredData(gameSchema, websiteSchema, organizationSchema);
}

export default async function HomePage() {
  // 获取核心游戏数据
  const game = await getGameBySlug(CORE_GAME_SLUG);
  
  // 如果核心游戏不存在，显示404
  if (!game) {
    notFound();
  }

  // 获取相关数据 - 新的逻辑
  const [relatedGames, newGames, hotGames] = await Promise.all([
    // Related Games: 获取Pokemon分类的游戏（因为核心游戏是Pokemon类型）
    getAllGameFrontmatters({ filterByGenre: 'Pokemon', sortByDate: true }).then(games => 
      games.filter(g => g.slug !== CORE_GAME_SLUG).slice(0, 24)
    ),
    // Latest Games: 获取更多最新游戏用于智能合并 - 增加到20个
    getAllGameFrontmatters({ sortByDate: true }).then(games => games.slice(0, 20)),
    // Hot Games: 获取热门游戏（保持站长配置的顺序）- 增加到15个
    getHotGameFrontmatters().then(games => games.slice(0, 15))
  ]);

  // 创建智能合并的侧边栏游戏列表
  const smartGameList = createSmartGameList(newGames, hotGames, CORE_GAME_SLUG);
  
  // 分配给左右侧边栏 - 调整为2×6布局，每个侧边栏12个游戏
  const leftGames = smartGameList.slice(0, 12);  // 取前12个给左侧边栏
  const rightGames = smartGameList.slice(12, 24); // 取第13-24个给右侧边栏

  const structuredData = generateHomePageStructuredData(game);

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={structuredData} />
      
      <div className="min-h-screen bg-background">
        {/* 主内容区域 - 全宽布局，响应侧边栏状态 */}
        <div className="w-full px-2 sm:px-4 lg:px-6 py-4">
          {/* Main game area - 方案A：扩大的GamePlayer容器 */}
          <div className="mb-6 flex justify-center">
            <div className="w-full max-w-[1800px]">
              <GamePlayer
                title={game.title}
                displayTitle={game.displayTitle}
                thumbnail={game.thumbnail}
                iframeUrl={game.iframeUrl}
                gameSlug={game.slug}
                gameData={{
                  title: game.title,
                  slug: game.slug,
                  thumbnail: game.thumbnail,
                  genres: game.genres,
                  description: game.description,
                  iframeUrl: game.iframeUrl,
                  publishedDate: game.publishedDate,
                  author: game.author,
                  version: game.version,
                  pageDescription: game.pageDescription
                }}
                leftGames={leftGames}
                rightGames={rightGames}
                className="w-full"
              />
            </div>
          </div>

          {/* Bottom carousel section - Related Games */}
          <div className="mb-6 flex justify-center">
            <div className="w-full max-w-[1800px] theme-card-elevated p-2 transition-shadow duration-300 hover:shadow-xl">
              <RelatedGamesCarousel
                games={relatedGames}
                title="Related Games"
                autoPlay={true}
                autoPlayInterval={4000}
                cardSize="medium"
                showTags={true}
              />
            </div>
          </div>

          {/* 底部区域：左侧内容说明 + 右侧留言板 */}
          <div className="flex justify-center">
            <div className="w-full max-w-[1800px]">
            {/* 游戏内容说明描述区 */}
            <div className="w-full">
              <div className="theme-card">
                <GameContentSection 
                  game={game}
                  showGameMeta={true}
                  className="p-0" // 移除默认padding，因为容器已有
                  contentClassName="prose prose-sm lg:prose-lg max-w-none"
                />
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
