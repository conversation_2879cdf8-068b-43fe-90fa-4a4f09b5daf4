import GameCard from '@/components/features/game/GameCard';
import { getAllGameFrontmatters } from '@/lib/api/markdown';
import { Metadata } from 'next';
import Link from 'next/link';

// Generate static metadata for SEO
export const metadata: Metadata = {
  title: 'Adventure Games',
  description: 'Discover amazing adventure games on our platform. Play the best adventure games online for free, explore captivating worlds and uncover mysteries.',
};

export default async function AdventureGamesPage() {
  // Get games filtered by Adventure genre and sorted by date
  const games = await getAllGameFrontmatters({ 
    filterByGenre: 'Adventure', 
    sortByDate: true 
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-secondary to-primary text-primary-foreground py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Adventure Games
          </h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Explore captivating worlds, solve mysteries, and embark on unforgettable journeys!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {games.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground">
                  Adventure Games ({games.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-2">
                {games.map((game) => (
                  <GameCard key={game.slug} game={game} />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🗺️</div>
              <h2 className="text-2xl font-bold text-foreground mb-4">
                No Adventure Games Found
              </h2>
              <p className="text-muted-foreground mb-8">
                We&apos;re working on adding more adventure games. Check back soon!
              </p>
              <Link
                href="/new-games"
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
} 