"use client";

import { useEffect } from "react";

interface AdSenseAdProps {
  // 广告位插槽ID
  adSlot: string;
  // 广告样式 - 响应式或固定尺寸
  adFormat?: "auto" | "rectangle" | "vertical" | "horizontal";
  // 是否为响应式广告
  isResponsive?: boolean;
  // 自定义样式
  style?: React.CSSProperties;
  // 广告位类名
  className?: string;
}

const AdSenseAd: React.FC<AdSenseAdProps> = ({
  adSlot,
  adFormat = "auto",
  isResponsive = true,
  style = {},
  className = "",
}) => {
  const adClient = process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_ID;

  useEffect(() => {
    if (!adClient) return;
    try {
      // 确保 adsbygoogle 已加载
      if (typeof window !== "undefined" && (window as any).adsbygoogle) {
        ((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
      }
    } catch (error) {
      console.error("AdSense 广告加载失败:", error);
    }
  }, [adClient]);

  if (!adClient) {
    return null;
  }

  const defaultStyle = {
    display: "block",
    ...style,
  };

  return (
    <div className={`adsense-container ${className}`}>
      <ins
        className="adsbygoogle"
        style={defaultStyle}
        data-ad-client={adClient}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-full-width-responsive={isResponsive.toString()}
      />
    </div>
  );
};

export default AdSenseAd; 