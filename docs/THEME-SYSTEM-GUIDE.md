# 主题系统开发规范

## 📋 概述

本文档定义了项目中主题系统的使用规范，确保所有组件都能正确响应 `theme.css` 中定义的变量，实现真正的主题切换功能。

## 🎯 核心原则

### 1. **theme.css 不可修改原则**
- `styles/theme.css` 是主题模板文件，**禁止在开发过程中修改**
- 该文件会根据不同项目需求进行整体替换
- 所有组件必须适配 `theme.css` 中已定义的变量

### 2. **禁用硬编码样式原则**
- **禁止使用** Tailwind 的硬编码颜色类（如：`bg-red-500`, `text-gray-600`）
- **必须使用** 主题变量或主题化工具类
- **例外情况**：品牌特定颜色（如社交媒体图标）可以使用硬编码

### 3. **语义化命名原则**
- 使用语义化的主题变量而非具体颜色
- 优先使用功能性描述而非视觉描述

## 🎨 可用主题变量

### 核心颜色变量
```css
/* 基础颜色 */
--background          /* 页面背景 */
--foreground          /* 主要文本 */
--card               /* 卡片背景 */
--card-foreground    /* 卡片文本 */
--popover            /* 弹出框背景 */
--popover-foreground /* 弹出框文本 */

/* 功能颜色 */
--primary            /* 主要操作 */
--primary-foreground /* 主要操作文本 */
--secondary          /* 次要操作 */
--secondary-foreground /* 次要操作文本 */
--accent             /* 强调元素 */
--accent-foreground  /* 强调元素文本 */
--destructive        /* 危险操作 */
--destructive-foreground /* 危险操作文本 */
--muted              /* 柔和背景 */
--muted-foreground   /* 柔和文本 */

/* 交互元素 */
--border             /* 边框颜色 */
--input              /* 输入框边框 */
--ring               /* 焦点环颜色 */
```

### 图表颜色变量
```css
--chart-1, --chart-2, --chart-3, --chart-4, --chart-5
```

### 侧边栏变量
```css
--sidebar, --sidebar-foreground, --sidebar-primary, 
--sidebar-primary-foreground, --sidebar-accent, 
--sidebar-accent-foreground, --sidebar-border, --sidebar-ring
```

### 尺寸变量
```css
/* 圆角 */
--radius-sm, --radius-md, --radius-lg, --radius-xl

/* 阴影 */
--shadow-2xs, --shadow-xs, --shadow-sm, --shadow, 
--shadow-md, --shadow-lg, --shadow-xl, --shadow-2xl

/* 字体 */
--font-sans, --font-serif, --font-mono

/* 间距 */
--spacing, --tracking-normal
```

## 🛠️ 主题化工具类

### 状态颜色类
```css
.theme-success    /* 成功状态 → var(--secondary) */
.theme-warning    /* 警告状态 → var(--accent) */
.theme-error      /* 错误状态 → var(--destructive) */
.theme-info       /* 信息状态 → var(--primary) */
```

### 按钮系统
```css
.theme-btn                /* 按钮基础样式 */
.theme-btn-primary        /* 主要按钮 */
.theme-btn-secondary      /* 次要按钮 */
.theme-btn-outline        /* 轮廓按钮 */
```

### 卡片系统
```css
.theme-card              /* 基础卡片 */
.theme-card-elevated     /* 提升卡片（更强阴影） */
```

### 标签和徽章
```css
.theme-badge             /* 徽章基础样式 */
.theme-badge-hot         /* 热门标签 */
.theme-badge-new         /* 新品标签 */
.theme-badge-featured    /* 精选标签 */
```

### 交互状态
```css
.theme-hover             /* 悬停效果 */
.theme-skeleton          /* 骨架屏 */
.theme-skeleton-dark     /* 深色骨架屏 */
```

### 文本和边框
```css
.theme-text-muted        /* 柔和文本 */
.theme-text-emphasis     /* 强调文本 */
.theme-border            /* 主题边框 */
.theme-divider           /* 分割线 */
```

## 📝 使用指南

### ✅ 正确示例

```tsx
// ✅ 使用主题变量
<div className="bg-card text-card-foreground border-border">
  <h2 className="text-foreground">标题</h2>
  <p className="text-muted-foreground">描述文本</p>
</div>

// ✅ 使用主题化工具类
<button className="theme-btn theme-btn-primary">
  主要操作
</button>

// ✅ 使用状态颜色
<div className="theme-success p-4 rounded-lg">
  操作成功
</div>
```

### ❌ 错误示例

```tsx
// ❌ 硬编码颜色
<div className="bg-gray-100 text-gray-900 border-gray-300">
  <h2 className="text-black">标题</h2>
  <p className="text-gray-600">描述文本</p>
</div>

// ❌ 硬编码按钮
<button className="bg-blue-600 hover:bg-blue-700 text-white">
  按钮
</button>

// ❌ 硬编码状态
<div className="bg-green-100 text-green-800 border-green-200">
  成功消息
</div>
```

## 🎯 组件开发最佳实践

### 1. 新组件开发
```tsx
// 组件模板
export default function MyComponent({ className = '' }: Props) {
  return (
    <div className={`theme-card p-4 ${className}`}>
      <h3 className="text-foreground font-medium mb-2">
        标题
      </h3>
      <p className="theme-text-muted text-sm">
        描述文本
      </p>
      <button className="theme-btn theme-btn-primary mt-4">
        操作按钮
      </button>
    </div>
  );
}
```

### 2. 条件样式处理
```tsx
// ✅ 正确的条件样式
const getStatusClass = (status: 'success' | 'error' | 'warning') => {
  const statusMap = {
    success: 'theme-success',
    error: 'theme-error', 
    warning: 'theme-warning'
  };
  return statusMap[status];
};

// ❌ 错误的条件样式
const getStatusClass = (status: string) => {
  if (status === 'success') return 'bg-green-500 text-white';
  if (status === 'error') return 'bg-red-500 text-white';
  return 'bg-yellow-500 text-black';
};
```

### 3. 响应式设计
```tsx
// ✅ 结合主题变量的响应式
<div className="theme-card p-4 md:p-6 lg:theme-card-elevated">
  <h2 className="text-lg md:text-xl text-foreground">
    响应式标题
  </h2>
</div>
```

## 🔍 代码审查清单

### 必检项目

#### 颜色使用
- [ ] 没有使用硬编码颜色类（`bg-red-500`, `text-gray-600` 等）
- [ ] 所有颜色都使用主题变量或主题化工具类
- [ ] 状态颜色使用语义化类（`theme-success`, `theme-error` 等）

#### 组件结构
- [ ] 容器使用 `theme-card` 或 `theme-card-elevated`
- [ ] 按钮使用 `theme-btn` 系列类
- [ ] 文本使用 `text-foreground` 或 `theme-text-muted`
- [ ] 边框使用 `border-border` 或 `theme-border`

#### 交互状态
- [ ] 悬停效果使用 `theme-hover`
- [ ] 加载状态使用 `theme-skeleton`
- [ ] 焦点状态使用 `focus:ring-ring`

#### 主题响应性
- [ ] 组件在浅色主题下显示正常
- [ ] 组件在深色主题下显示正常
- [ ] 主题切换时无视觉闪烁
- [ ] 所有交互元素在两种主题下都可用

## 🧪 测试指南

### 主题切换测试
1. 在浅色主题下检查组件
2. 切换到深色主题
3. 验证所有元素正确响应
4. 检查对比度和可读性
5. 测试交互状态（hover, focus, active）

### 视觉回归测试
- 截图对比测试
- 跨浏览器兼容性
- 移动端响应式测试

## 🚨 常见错误和解决方案

### 错误1：使用硬编码颜色
```tsx
// ❌ 错误
<div className="bg-gray-100 text-gray-800">

// ✅ 修正
<div className="bg-muted text-foreground">
```

### 错误2：忘记处理深色主题
```tsx
// ❌ 错误
<div className="bg-white text-black border-gray-200">

// ✅ 修正  
<div className="theme-card">
```

### 错误3：状态颜色不统一
```tsx
// ❌ 错误
<div className="bg-green-500 text-white"> {/* 成功 */}
<div className="bg-emerald-600 text-white"> {/* 另一个成功 */}

// ✅ 修正
<div className="theme-success"> {/* 统一成功样式 */}
<div className="theme-success"> {/* 统一成功样式 */}
```

## 📚 扩展资源

### 主题展示页面
访问 `/theme-showcase` 查看所有主题化组件的实际效果

### 工具类参考
查看 `styles/globals.css` 中的 `@layer utilities` 部分了解所有可用工具类

### 变量定义
查看 `styles/theme.css` 了解所有可用的 CSS 变量定义

---

**记住：主题系统的目标是让您能够通过简单替换 `theme.css` 文件来完全改变整个应用的视觉风格，而无需修改任何组件代码。** 