/* AdSense 广告样式 */

/* 广告容器基础样式 */
.adsense-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* 横幅广告样式 */
.banner-ad {
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border);
  background-color: var(--card);
}

/* 侧边栏广告样式 */
.sidebar-ad {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  background-color: var(--card);
}

/* 内容内广告样式 */
.in-content-ad {
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border);
  background-color: var(--card);
}


/* 广告加载状态 */
.adsense-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0.5;
  z-index: -1;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-ad {
    max-width: 320px;
    height: 50px;
  }
  
  .sidebar-ad {
    max-width: 300px;
    height: 250px;
  }
}

@media (max-width: 480px) {
  .banner-ad {
    max-width: 280px;
    height: 50px;
  }
  
  .sidebar-ad {
    max-width: 250px;
    height: 200px;
  }
}