"use client";

import React from 'react';
import Link from 'next/link';
import { Home, Gamepad2, Heart, Settings, Menu, X } from 'lucide-react';

interface EnhancedSidebarProps {
  /** 是否展开 */
  isOpen?: boolean;
  /** 切换展开状态的回调 */
  onToggle?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 增强的侧边栏组件，使用theme.css中定义的sidebar相关变量
 */
export default function EnhancedSidebar({ 
  isOpen = false, 
  onToggle, 
  className = '' 
}: EnhancedSidebarProps) {
  return (
    <>
      {/* 移动端遮罩层 */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden mobile-overlay visible"
          onClick={onToggle}
        />
      )}
      
      {/* 侧边栏切换按钮 */}
      <button
        onClick={onToggle}
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-sidebar rounded-full shadow-md"
        aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
      >
        {isOpen ? (
          <X className="w-5 h-5 text-sidebar-foreground" />
        ) : (
          <Menu className="w-5 h-5 text-sidebar-foreground" />
        )}
      </button>
      
      {/* 侧边栏主体 */}
      <aside 
        className={`
          fixed top-0 left-0 h-full w-64 z-40
          transform transition-transform duration-300
          md:translate-x-0
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
          ${className}
        `}
      >
        <div className="sidebar-content h-full">
          {/* 侧边栏标题 */}
          <div className="p-4 border-b border-sidebar-border bg-sidebar flex items-center justify-between">
            <h2 className="text-lg font-semibold text-sidebar-foreground">游戏菜单</h2>
            <button
              onClick={onToggle}
              className="md:hidden p-1 rounded-full hover:bg-sidebar-accent/10"
              aria-label="Close sidebar"
            >
              <X className="w-5 h-5 text-sidebar-foreground" />
            </button>
          </div>
          
          {/* 侧边栏菜单 */}
          <nav className="p-4 space-y-2">
            <Link 
              href="/"
              className="flex items-center gap-3 p-2 rounded hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
            >
              <Home className="w-5 h-5" />
              <span>首页</span>
            </Link>
            
            <Link 
              href="/games"
              className="flex items-center gap-3 p-2 rounded hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
            >
              <Gamepad2 className="w-5 h-5" />
              <span>游戏</span>
            </Link>
            
            <Link 
              href="/favorites"
              className="flex items-center gap-3 p-2 rounded hover:bg-sidebar-primary hover:text-sidebar-primary-foreground transition-colors"
            >
              <Heart className="w-5 h-5" />
              <span>收藏</span>
            </Link>
            
            <Link 
              href="/settings"
              className="flex items-center gap-3 p-2 rounded hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
            >
              <Settings className="w-5 h-5" />
              <span>设置</span>
            </Link>
          </nav>
          
          {/* 侧边栏底部 */}
          <div className="mt-auto p-4 border-t border-sidebar-border">
            <div className="text-xs text-sidebar-foreground/70">
              此组件使用了theme.css中定义的sidebar相关变量
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}