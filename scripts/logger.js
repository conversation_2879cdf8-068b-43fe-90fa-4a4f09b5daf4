const fs = require('fs');
const path = require('path');
const Utils = require('./utils');

/**
 * 增强日志系统
 */
class Logger {
  constructor(options = {}) {
    this.logDir = options.logDir || 'scripts/logs';
    this.logLevel = options.logLevel || 'info';
    this.maxLogFiles = options.maxLogFiles || 10;
    this.maxLogSize = options.maxLogSize || 10 * 1024 * 1024;
    
    this.session = {
      id: this.generateSessionId(),
      startTime: new Date(),
      operations: [],
      errors: [],
      warnings: []
    };

    this.initializeLogger();
  }

  initializeLogger() {
    Utils.ensureDirectoryExists(this.logDir);
    this.cleanupOldLogs();
    
    this.sessionLogFile = path.join(this.logDir, `session-${this.session.id}.log`);
    this.errorLogFile = path.join(this.logDir, `errors-${new Date().toISOString().split('T')[0]}.log`);
    
    this.writeLog('info', 'Logger initialized', { sessionId: this.session.id });
  }

  generateSessionId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}-${random}`;
  }

  info(message, data = {}) {
    this.log('info', message, data);
  }

  warn(message, data = {}) {
    this.log('warn', message, data);
    this.session.warnings.push({
      message,
      data,
      timestamp: new Date()
    });
  }

  error(message, error = {}) {
    const errorData = this.processError(error);
    this.log('error', message, errorData);
    
    this.session.errors.push({
      message,
      error: errorData,
      timestamp: new Date()
    });

    this.writeErrorLog(message, errorData);
  }

  debug(message, data = {}) {
    if (this.logLevel === 'debug') {
      this.log('debug', message, data);
    }
  }

  startOperation(operation, params = {}) {
    const operationId = this.generateOperationId();
    const operationData = {
      id: operationId,
      name: operation,
      params,
      startTime: new Date(),
      status: 'running'
    };

    this.session.operations.push(operationData);
    this.info(`Operation started: ${operation}`, { operationId, params });
    
    return operationId;
  }

  endOperation(operationId, result = {}) {
    const operation = this.session.operations.find(op => op.id === operationId);
    if (operation) {
      operation.endTime = new Date();
      operation.duration = operation.endTime - operation.startTime;
      operation.result = result;
      operation.status = 'completed';
      
      this.info(`Operation completed: ${operation.name}`, {
        operationId,
        duration: operation.duration,
        result
      });
    }
  }

  failOperation(operationId, error) {
    const operation = this.session.operations.find(op => op.id === operationId);
    if (operation) {
      operation.endTime = new Date();
      operation.duration = operation.endTime - operation.startTime;
      operation.error = this.processError(error);
      operation.status = 'failed';
      
      this.error(`Operation failed: ${operation.name}`, {
        operationId,
        duration: operation.duration,
        error: operation.error
      });
    }
  }

  log(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      sessionId: this.session.id,
      message,
      data
    };

    this.consoleOutput(level, message, data);
    this.writeLog(level, message, data);
  }

  consoleOutput(level, message, data) {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
      debug: '🔍',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌'
    };
    
    const icon = icons[level] || 'ℹ️';
    let output = `${icon} [${timestamp}] ${message}`;
    
    if (Object.keys(data).length > 0) {
      output += ` ${JSON.stringify(data)}`;
    }
    
    console.log(output);
  }

  writeLog(level, message, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      sessionId: this.session.id,
      message,
      data
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    
    try {
      fs.appendFileSync(this.sessionLogFile, logLine);
    } catch (error) {
      console.error('Failed to write log:', error.message);
    }
  }

  writeErrorLog(message, errorData) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      sessionId: this.session.id,
      message,
      error: errorData,
      stack: errorData.stack
    };

    const errorLine = JSON.stringify(errorEntry) + '\n';
    
    try {
      fs.appendFileSync(this.errorLogFile, errorLine);
    } catch (error) {
      console.error('Failed to write error log:', error.message);
    }
  }

  processError(error) {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: 'Error'
      };
    } else if (typeof error === 'object') {
      return {
        ...error,
        type: 'CustomError'
      };
    } else {
      return {
        message: String(error),
        type: 'Unknown'
      };
    }
  }

  generateOperationId() {
    return `op-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  getSessionStats() {
    const now = new Date();
    const duration = now - this.session.startTime;
    
    return {
      sessionId: this.session.id,
      startTime: this.session.startTime,
      duration: Math.round(duration / 1000),
      operations: {
        total: this.session.operations.length,
        completed: this.session.operations.filter(op => op.status === 'completed').length,
        failed: this.session.operations.filter(op => op.status === 'failed').length,
        running: this.session.operations.filter(op => op.status === 'running').length
      },
      errors: this.session.errors.length,
      warnings: this.session.warnings.length
    };
  }

  generateSessionReport() {
    const stats = this.getSessionStats();
    let report = '';
    
    report += `📊 会话报告 (ID: ${stats.sessionId})\n`;
    report += `⏱️  持续时间: ${stats.duration} 秒\n`;
    report += `🔧 操作统计:\n`;
    report += `   总计: ${stats.operations.total}\n`;
    report += `   成功: ${stats.operations.completed}\n`;
    report += `   失败: ${stats.operations.failed}\n`;
    report += `   运行中: ${stats.operations.running}\n`;
    
    if (stats.errors > 0) {
      report += `❌ 错误: ${stats.errors}\n`;
    }
    
    if (stats.warnings > 0) {
      report += `⚠️  警告: ${stats.warnings}\n`;
    }

    if (this.session.errors.length > 0) {
      report += `\n🔴 最近的错误:\n`;
      this.session.errors.slice(-3).forEach(error => {
        report += `   • ${error.message}\n`;
      });
    }

    return report;
  }

  cleanupOldLogs() {
    try {
      const files = fs.readdirSync(this.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          stats: fs.statSync(path.join(this.logDir, file))
        }))
        .sort((a, b) => b.stats.mtime - a.stats.mtime);

      if (logFiles.length > this.maxLogFiles) {
        const filesToDelete = logFiles.slice(this.maxLogFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          console.log(`🗑️  删除旧日志文件: ${file.name}`);
        });
      }
    } catch (error) {
      console.warn('清理日志文件失败:', error.message);
    }
  }

  endSession() {
    this.info('Session ending', this.getSessionStats());
    
    const report = this.generateSessionReport();
    const summaryFile = path.join(this.logDir, `summary-${this.session.id}.txt`);
    
    try {
      fs.writeFileSync(summaryFile, report);
    } catch (error) {
      console.error('Failed to write session summary:', error.message);
    }
    
    console.log('\n' + report);
  }
}

module.exports = Logger;
