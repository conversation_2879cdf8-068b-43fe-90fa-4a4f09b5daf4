import { siteConfig } from '@/config/site';

// 游戏数据类型定义
interface GameData {
  title: string;
  slug: string;
  description: string;
  thumbnail: string;
  genres: string[];
  publishedDate: string;
  author?: string;
  version?: string;
  iframeUrl: string;
  pageDescription?: string;
}

  // 面包屑项目类型
interface BreadcrumbItem {
  name: string;
  url: string;
}

/**
 * 生成VideoGame结构化数据
 */
export function generateVideoGameSchema(game: GameData, url: string) {
  return {
    '@context': 'https://schema.org',
    '@type': 'VideoGame',
    name: game.title,
    description: game.pageDescription || game.description,
    image: game.thumbnail,
    url: url,
    genre: game.genres,
    datePublished: game.publishedDate,
    version: game.version || '1.0',
    gamePlatform: ['Web Browser', 'Online'],
    gameMode: 'Single Player',
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      url: siteConfig.url,
    },
    developer: game.author ? {
      '@type': 'Person',
      name: game.author,
    } : {
      '@type': 'Organization',
      name: siteConfig.name,
    },
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
      category: 'Free',
    },
    applicationCategory: 'Game',
    operatingSystem: 'Web Browser',
    softwareRequirements: 'Modern Web Browser with JavaScript enabled',
    contentRating: 'Everyone',
    inLanguage: 'en-US',
  };
}



/**
 * 生成网站组织结构化数据
 */
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    url: siteConfig.url,
    logo: `${siteConfig.url}/android-chrome-512x512.png`,
    description: siteConfig.description,
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      url: `${siteConfig.url}/contact-us`,
    },
    sameAs: [
      // 可以添加社交媒体链接
    ],
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };
}

/**
 * 生成网站结构化数据
 */
export function generateWebSiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    inLanguage: 'en-US',
  };
}

/**
 * 生成游戏分类页面结构化数据
 */
export function generateCollectionPageSchema(
  categoryName: string,
  categoryDescription: string,
  categoryUrl: string,
  gamesCount: number
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: categoryName,
    description: categoryDescription,
    url: categoryUrl,
    mainEntity: {
      '@type': 'ItemList',
      name: categoryName,
      description: categoryDescription,
      numberOfItems: gamesCount,
      itemListElement: [], // 可以添加具体的游戏列表
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: siteConfig.url,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Games',
          item: `${siteConfig.url}/games`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: categoryName,
          item: categoryUrl,
        },
      ],
    },
  };
}

/**
 * 生成博客文章结构化数据
 */
export function generateArticleSchema(
  blog: {
    title: string;
    description: string;
    slug: string;
    publishedDate: string;
    author: string;
    thumbnail: string;
  },
  url: string
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: blog.title,
    description: blog.description,
    url: url,
    datePublished: blog.publishedDate,
    dateModified: blog.publishedDate, // 简单处理，将修改日期设为发布日期
    author: {
      '@type': 'Person', // 假定博客作者是个人
      name: blog.author,
    },
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      logo: {
        '@type': 'ImageObject',
        url: `${siteConfig.url}/android-chrome-512x512.png`,
      },
    },
    image: {
      '@type': 'ImageObject',
      url: blog.thumbnail,
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url,
    },
    inLanguage: 'en-US',
  };
}



/**
 * 合并多个结构化数据对象
 */
export function combineStructuredData(...schemas: any[]) {
  return schemas.filter(Boolean);
} 