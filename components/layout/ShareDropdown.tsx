"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Share2, Copy, ExternalLink } from 'lucide-react';

interface ShareDropdownProps {
  /** 分享标题 */
  title: string;
  /** 游戏slug */
  gameSlug: string;
  /** 自定义样式类名 */
  className?: string;
}

// 社交媒体平台配置
interface SocialPlatform {
  name: string;
  icon: string;
  shareUrl: (url: string, title: string) => string;
  color: string; // 使用主题变量映射
}

const socialPlatforms: SocialPlatform[] = [
  {
    name: 'Facebook',
    icon: '📘',
    shareUrl: (url, title) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(title)}`,
    color: 'theme-info' // 映射到主题的info颜色
  },
  {
    name: 'Twitter',
    icon: '🐦',
    shareUrl: (url, title) => `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
    color: 'theme-info'
  },
  {
    name: 'Reddit',
    icon: '🤖',
    shareUrl: (url, title) => `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
    color: 'theme-warning'
  },
  {
    name: 'Pinterest',
    icon: '📌',
    shareUrl: (url, title) => `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(title)}`,
    color: 'theme-error'
  }
];

export default function ShareDropdown({ 
  title, 
  gameSlug, 
  className = '' 
}: ShareDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 生成完整的游戏URL
  const gameUrl = `${typeof window !== 'undefined' ? window.location.origin : ''}/${gameSlug}`;

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 复制链接到剪贴板
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(gameUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
      // 降级方案：使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = gameUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // 处理社交媒体分享
  const handleSocialShare = (platform: SocialPlatform) => {
    const shareUrl = platform.shareUrl(gameUrl, title);
    window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 分享按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="theme-btn theme-btn-outline p-2 rounded-full theme-hover transition-colors"
        title="Share this game"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Share2 className="w-5 h-5" />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="
          absolute right-0 top-full mt-2 
          theme-card theme-card-elevated
          w-64 z-50
          animate-in fade-in-0 zoom-in-95 slide-in-from-top-2
          duration-200
        ">
          <div className="p-3">
            {/* 标题 */}
            <div className="mb-3">
              <h3 className="text-sm font-semibold text-foreground mb-1">
                Share this game
              </h3>
              <p className="text-xs theme-text-muted line-clamp-2">
                {title}
              </p>
            </div>

            {/* 社交媒体按钮 */}
            <div className="space-y-1 mb-3">
              {socialPlatforms.map((platform) => (
                <button
                  key={platform.name}
                  onClick={() => handleSocialShare(platform)}
                  className="
                    w-full flex items-center gap-3 p-2 rounded
                    theme-hover text-left
                    transition-colors duration-200
                  "
                >
                  <span className="text-lg">{platform.icon}</span>
                  <span className="text-sm text-foreground">
                    Share on {platform.name}
                  </span>
                  <ExternalLink className="w-3 h-3 theme-text-muted ml-auto" />
                </button>
              ))}
            </div>

            {/* 分割线 */}
            <hr className="theme-divider my-3" />

            {/* 复制链接按钮 */}
            <button
              onClick={handleCopyLink}
              className="
                w-full flex items-center gap-3 p-2 rounded
                theme-hover
                transition-colors duration-200
              "
            >
              <Copy className="w-4 h-4" />
              <span className="text-sm text-foreground">
                {copied ? 'Link copied!' : 'Copy link'}
              </span>
              {copied && (
                <span className="text-xs theme-text-muted ml-auto">✓</span>
              )}
            </button>

            {/* 游戏URL预览 */}
            <div className="mt-3 p-2 bg-muted rounded">
              <p className="text-xs theme-text-muted break-all">
                {gameUrl}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 