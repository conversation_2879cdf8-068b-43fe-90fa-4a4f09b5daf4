import { getBlogBySlug, getBlogSlugs } from '@/lib/api/blog';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import { generateBlogImageAlt } from '@/lib/utils/alt-text-utils';
import StructuredData from '@/components/common/StructuredData';
import { generateArticleSchema } from '@/lib/constants/structured-data';
import { siteConfig } from '@/config/site';

interface BlogPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate static params for all blogs
export async function generateStaticParams() {
  const slugs = getBlogSlugs();
  
  return slugs.map((slug) => ({
    slug: slug,
  }));
}

// Generate dynamic metadata for SEO
export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const blogSlug = resolvedParams.slug;
  const blog = await getBlogBySlug(blogSlug);
  
  if (!blog) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: blog.title,
    description: blog.description,
    authors: [{ name: blog.author }],
    openGraph: {
      title: blog.title,
      description: blog.description,
      type: 'article',
      url: `/blog/${blogSlug}`,
      images: [
        {
          url: blog.thumbnail,
          width: 1200,
          height: 630,
          alt: generateBlogImageAlt(blog.title),
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
      publishedTime: blog.publishedDate,
    },
    twitter: {
      card: 'summary_large_image',
      title: blog.title,
      description: blog.description,
      images: [blog.thumbnail],
    },
    alternates: {
      canonical: `/blog/${blogSlug}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function BlogPage({ params }: BlogPageProps) {
  const resolvedParams = await params;
  const blogSlug = resolvedParams.slug;
  const blog = await getBlogBySlug(blogSlug);
  
  // If blog is not found, show 404 page
  if (!blog) {
    notFound();
  }

  const blogUrl = `${siteConfig.url}/blog/${blogSlug}`;
  const articleSchema = generateArticleSchema(blog, blogUrl);

  return (
    <>
      <StructuredData data={articleSchema} />
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Blog Header */}
          <article className="theme-card-elevated overflow-hidden">
            {/* Featured Image */}
            <div className="relative w-full h-64 md:h-80">
              <Image
                src={blog.thumbnail}
                alt={generateBlogImageAlt(blog.title)}
                fill
                className="object-cover"
                priority
              />
            </div>

            {/* Blog Content */}
            <div className="p-6 md:p-8">
              {/* Blog Meta */}
              <div className="mb-6">
                <div className="flex items-center text-sm text-muted-foreground mb-4">
                  <span>By {blog.author}</span>
                  <span className="mx-2">•</span>
                  <time dateTime={blog.publishedDate}>
                    {new Date(blog.publishedDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </time>
                </div>
                
                <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                  {blog.title}
                </h1>
                
                <p className="text-lg text-muted-foreground">
                  {blog.description}
                </p>
              </div>

              {/* Blog Content */}
              <div 
                className="prose prose-lg dark:prose-invert max-w-none prose-headings:text-foreground prose-p:text-foreground prose-a:text-primary prose-strong:text-foreground"
                dangerouslySetInnerHTML={{ __html: blog.contentHtml }}
              />
            </div>
          </article>
        </div>
      </div>
    </>
  );
} 