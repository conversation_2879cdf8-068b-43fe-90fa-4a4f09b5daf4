require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const Utils = require('./utils');

/**
 * 批量处理器
 */
class BatchProcessor {
  constructor(options = {}) {
    this.batchSize = options.batchSize || 5;
    this.delayBetweenBatches = options.delayBetweenBatches || 2000;
    this.delayBetweenRequests = options.delayBetweenRequests || 200;
    this.maxRetries = options.maxRetries || 3;
    this.resumeFile = options.resumeFile || 'scripts/.batch-progress.json';
    
    this.totalGames = 0;
    this.processedGames = 0;
    this.successCount = 0;
    this.failCount = 0;
    this.startTime = Date.now();
    this.failedGames = [];
  }

  async processBatch(games, processFunction, options = {}) {
    this.totalGames = games.length;
    this.processedGames = 0;
    this.successCount = 0;
    this.failCount = 0;
    this.failedGames = [];

    console.log(`🚀 开始批量处理 ${this.totalGames} 个游戏`);
    this.showProgressBar();

    const resumeData = this.loadResumeData();
    let startIndex = 0;
    
    if (resumeData && resumeData.lastProcessedIndex !== undefined) {
      startIndex = resumeData.lastProcessedIndex + 1;
      this.processedGames = startIndex;
      console.log(`📂 检测到断点续传文件，从第 ${startIndex + 1} 个游戏开始`);
    }

    for (let i = startIndex; i < games.length; i += this.batchSize) {
      const batch = games.slice(i, Math.min(i + this.batchSize, games.length));
      const batchNumber = Math.floor(i / this.batchSize) + 1;
      const totalBatches = Math.ceil((games.length - startIndex) / this.batchSize);

      console.log(`�� 处理第 ${batchNumber}/${totalBatches} 批 (${batch.length} 个游戏)`);

      await this.processSingleBatch(batch, processFunction, i);
      this.saveResumeData(i + batch.length - 1);

      if (i + this.batchSize < games.length) {
        console.log(`⏳ 等待 ${this.delayBetweenBatches}ms 后处理下一批...`);
        await Utils.delay(this.delayBetweenBatches);
      }
    }

    this.clearResumeData();
    this.showFinalStats();

    return {
      total: this.totalGames,
      success: this.successCount,
      failed: this.failCount,
      failedGames: this.failedGames,
      duration: Math.round((Date.now() - this.startTime) / 1000)
    };
  }

  async processSingleBatch(batch, processFunction, startIndex) {
    for (let i = 0; i < batch.length; i++) {
      const game = batch[i];
      const gameIndex = startIndex + i;
      
      try {
        console.log(`🎮 [${gameIndex + 1}/${this.totalGames}] 处理: ${game.name}`);
        await processFunction(game, gameIndex);
        this.successCount++;
        this.updateProgressBar();
        
        if (i < batch.length - 1) {
          await Utils.delay(this.delayBetweenRequests);
        }
        
      } catch (error) {
        console.log(`❌ [${gameIndex + 1}/${this.totalGames}] 失败: ${game.name} - ${error.message}`);
        this.failCount++;
        this.failedGames.push({
          index: gameIndex,
          game: game,
          error: error.message
        });
        this.updateProgressBar();
      }
      
      this.processedGames++;
    }
  }

  showProgressBar() {
    const progress = this.totalGames > 0 ? (this.processedGames / this.totalGames * 100) : 0;
    const barLength = 30;
    const filledLength = Math.round(barLength * progress / 100);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    
    process.stdout.write(`\r📊 进度: [${bar}] ${progress.toFixed(1)}% (${this.processedGames}/${this.totalGames}) ✅${this.successCount} ❌${this.failCount}`);
  }

  updateProgressBar() {
    this.showProgressBar();
  }

  showFinalStats() {
    console.log('\n');
    const duration = Math.round((Date.now() - this.startTime) / 1000);
    const avgTime = this.processedGames > 0 ? (duration / this.processedGames).toFixed(1) : 0;
    
    console.log('🎉 批量处理完成！');
    console.log(`📊 统计信息:`);
    console.log(`   总计: ${this.totalGames} 个游戏`);
    console.log(`   成功: ${this.successCount} 个`);
    console.log(`   失败: ${this.failCount} 个`);
    console.log(`   耗时: ${duration} 秒`);
    console.log(`   平均: ${avgTime} 秒/游戏`);

    if (this.failedGames.length > 0) {
      console.log('❌ 失败的游戏:');
      this.failedGames.forEach(failed => {
        console.log(`   ${failed.index + 1}. ${failed.game.name}: ${failed.error}`);
      });
    }
  }

  saveResumeData(lastIndex) {
    const resumeData = {
      lastProcessedIndex: lastIndex,
      timestamp: new Date().toISOString(),
      stats: {
        total: this.totalGames,
        processed: this.processedGames,
        success: this.successCount,
        failed: this.failCount
      }
    };

    try {
      Utils.ensureDirectoryExists(path.dirname(this.resumeFile));
      fs.writeFileSync(this.resumeFile, JSON.stringify(resumeData, null, 2));
    } catch (error) {
      console.log(`⚠️ 保存断点续传数据失败: ${error.message}`);
    }
  }

  loadResumeData() {
    try {
      if (fs.existsSync(this.resumeFile)) {
        const data = fs.readFileSync(this.resumeFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.log(`⚠️ 读取断点续传数据失败: ${error.message}`);
    }
    return null;
  }

  clearResumeData() {
    try {
      if (fs.existsSync(this.resumeFile)) {
        fs.unlinkSync(this.resumeFile);
      }
    } catch (error) {
      console.log(`⚠️ 清理断点续传数据失败: ${error.message}`);
    }
  }
}

module.exports = BatchProcessor;
