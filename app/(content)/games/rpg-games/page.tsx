import GameCard from '@/components/features/game/GameCard';
import StructuredData from '@/components/common/StructuredData';
import { getAllGameFrontmatters } from '@/lib/api/markdown';
import { generateCollectionPageSchema } from '@/lib/constants/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import Link from 'next/link';

// Generate static metadata for SEO
export const metadata: Metadata = {
  title: 'RPG Games',
  description: 'Discover amazing RPG games on our platform. Play the best role-playing games online for free, create characters, explore vast worlds, and embark on epic quests.',
};

export default async function RPGGamesPage() {
  // Get games filtered by RPG genre and sorted by date
  const games = await getAllGameFrontmatters({ 
    filterByGenre: 'RPG', 
    sortByDate: true 
  });

  // 生成结构化数据
  const categoryUrl = `${siteConfig.url}/games/rpg-games`;
  const collectionSchema = generateCollectionPageSchema(
    'RPG Games',
    'Discover amazing RPG games on our platform. Play the best role-playing games online for free, create characters, explore vast worlds, and embark on epic quests.',
    categoryUrl,
    games.length
  );

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={collectionSchema} />
      
      <div className="min-h-screen bg-background">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-accent to-primary text-primary-foreground py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            RPG Games
          </h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Create characters, explore vast worlds, and embark on epic quests in these role-playing adventures!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {games.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground">
                  RPG Games ({games.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-2">
                {games.map((game) => (
                  <GameCard key={game.slug} game={game} />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">⚔️</div>
              <h2 className="text-2xl font-bold text-foreground mb-4">
                No RPG Games Found
              </h2>
              <p className="text-muted-foreground mb-8">
                We&apos;re working on adding more RPG games. Check back soon!
              </p>
              <Link
                href="/new-games"
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </section>
      </div>
    </>
  );
} 