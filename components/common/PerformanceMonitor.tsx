'use client';

import { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  /** 是否启用性能监控 */
  enabled?: boolean;
  /** 是否发送到Google Analytics */
  sendToGA?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = true,
  sendToGA = true,
  debug = false,
}) => {
  const metricsRef = useRef<Record<string, number>>({});

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    // 基础性能监控（不依赖web-vitals）
    const measurePerformance = () => {
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics = {
            dns: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcp: navigation.connectEnd - navigation.connectStart,
            request: navigation.responseStart - navigation.requestStart,
            response: navigation.responseEnd - navigation.responseStart,
            dom: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            load: navigation.loadEventEnd - navigation.loadEventStart,
            ttfb: navigation.responseStart - navigation.requestStart,
          };

          metricsRef.current = metrics;

          if (debug) {
            console.log('📊 Performance Metrics:', metrics);
          }

          // 发送到Google Analytics
          if (sendToGA && (window as any).gtag) {
            Object.entries(metrics).forEach(([key, value]) => {
              if (value > 0) {
                (window as any).gtag('event', 'performance_metric', {
                  event_category: 'Performance',
                  event_label: key,
                  value: Math.round(value),
                  non_interaction: true,
                });
              }
            });
          }
        }
      }
    };

    // 页面加载完成后测量
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, [enabled, sendToGA, debug]);

  return null; // 这是一个监控组件，不渲染任何内容
};

export default PerformanceMonitor;
