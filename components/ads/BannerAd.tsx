"use client";

import AdSenseAd from '@/components/common/ads/AdSenseAd';

interface BannerAdProps {
  className?: string;
}

/**
 * 横幅广告组件 - 适用于页面顶部或底部
 * 使用响应式设计，在移动端和桌面端都有良好的显示效果
 */
const BannerAd: React.FC<BannerAdProps> = ({ 
  className = "" 
}) => {
  const adSlot = process.env.NEXT_PUBLIC_AD_SLOT_BANNER;

  if (!adSlot) {
    return null;
  }

  return (
    <div className={`w-full flex justify-center my-4 ${className}`}>
      <AdSenseAd
        adSlot={adSlot}
        adFormat="auto"
        isResponsive={true}
        style={{
          width: "100%",
          maxWidth: "728px",
          height: "90px",
        }}
        className="banner-ad bg-card border-border"
      />
    </div>
  );
};

export default BannerAd; 