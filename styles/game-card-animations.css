/* 游戏卡片悬停动画效果 */

/* 确保变换原点在中心 */
.game-card-container {
  transform-origin: center;
}

/* 平滑的缩放动画 */
.game-card-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-card-scale:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
}

/* 图片的视差效果 */
.game-card-image {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.game-card-container:hover .game-card-image {
  transform: scale(1.1);
}

/* 信息面板滑入效果 */
.game-card-info {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* 播放按钮缩放效果 */
.game-card-play-button {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* 遮罩层渐变效果 */
.game-card-overlay {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: background-color;
}

/* 减少动画在移动设备上的性能影响 */
@media (prefers-reduced-motion: reduce) {
  .game-card-scale,
  .game-card-image,
  .game-card-info,
  .game-card-play-button,
  .game-card-overlay {
    transition: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .game-card-scale:active {
    transform: scale(1.02);
  }
  
  .game-card-container:active .game-card-image {
    transform: scale(1.05);
  }
}