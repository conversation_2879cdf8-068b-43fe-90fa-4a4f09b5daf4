const fs = require('fs');
const path = require('path');

/**
 * 工具函数集合
 */
class Utils {
  /**
   * 确保目录存在，如果不存在则创建
   * @param {string} dirPath 目录路径
   */
  static ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ 创建目录: ${dirPath}`);
    }
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath 文件路径
   * @returns {boolean}
   */
  static fileExists(filePath) {
    return fs.existsSync(filePath);
  }

  /**
   * 生成游戏slug（URL友好的标识符）
   * @param {string} gameName 游戏名称
   * @returns {string}
   */
  static generateSlug(gameName) {
    return gameName
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-')     // 空格替换为连字符
      .replace(/-+/g, '-')      // 多个连字符合并为一个
      .trim();
  }

  /**
   * 格式化当前日期为ISO字符串
   * @returns {string}
   */
  static getCurrentDate() {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * 延迟执行（用于API调用间隔）
   * @param {number} ms 延迟毫秒数
   * @returns {Promise}
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 安全地写入文件
   * @param {string} filePath 文件路径
   * @param {string} content 文件内容
   */
  static writeFileSync(filePath, content) {
    try {
      // 确保目录存在
      const dir = path.dirname(filePath);
      this.ensureDirectoryExists(dir);
      
      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 文件已创建: ${filePath}`);
    } catch (error) {
      console.error(`❌ 文件写入失败: ${filePath}`, error.message);
      throw error;
    }
  }

  /**
   * 读取JSON文件
   * @param {string} filePath JSON文件路径
   * @returns {Object}
   */
  static readJsonFile(filePath) {
    try {
      if (!this.fileExists(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      console.error(`❌ JSON文件读取失败: ${filePath}`, error.message);
      throw error;
    }
  }

  /**
   * 验证游戏数据格式
   * @param {Object} gameData 游戏数据
   * @returns {boolean} 是否有效
   */
  static validateGameData(gameData) {
    if (!gameData || typeof gameData !== 'object') {
      this.log('游戏数据必须是对象', 'error');
      return false;
    }

    // 必需字段验证
    const requiredFields = [
      { field: 'name', type: 'string', message: '游戏名称' },
      { field: 'iframeUrl', type: 'string', message: 'iframe链接' },
      { field: 'genres', type: 'array', message: '游戏类型标签' },
      { field: 'gameDescription', type: 'string', message: '游戏说明' }
    ];

    for (const { field, type, message } of requiredFields) {
      if (!gameData[field]) {
        this.log(`缺少必需字段: ${message} (${field})`, 'error');
        return false;
      }

      if (type === 'string' && typeof gameData[field] !== 'string') {
        this.log(`${message}必须是字符串`, 'error');
        return false;
      }

      if (type === 'array' && !Array.isArray(gameData[field])) {
        this.log(`${message}必须是数组`, 'error');
        return false;
      }

      if (type === 'string' && gameData[field].trim().length === 0) {
        this.log(`${message}不能为空`, 'error');
        return false;
      }

      if (type === 'array' && gameData[field].length === 0) {
        this.log(`${message}不能为空数组`, 'error');
        return false;
      }
    }

    // 验证游戏名称长度
    if (gameData.name.length < 2 || gameData.name.length > 100) {
      this.log('游戏名称长度应在2-100字符之间', 'error');
      return false;
    }

    // 验证iframe URL格式
    if (!this.isValidUrl(gameData.iframeUrl)) {
      this.log('iframe链接格式无效', 'error');
      return false;
    }

    // 验证游戏类型标签
    if (gameData.genres.some(genre => typeof genre !== 'string' || genre.trim().length === 0)) {
      this.log('游戏类型标签必须是非空字符串数组', 'error');
      return false;
    }

    // 验证游戏说明长度
    if (gameData.gameDescription.length < 10 || gameData.gameDescription.length > 2000) {
      this.log('游戏说明长度应在10-2000字符之间', 'error');
      return false;
    }

    // 自动生成缺失的可选字段
    if (!gameData.thumbnail) {
      // 自动生成图片路径：游戏名称转换为文件名格式
      const imageName = this.generateSlug(gameData.name) + '.png';
      gameData.thumbnail = `/images/thumbnails/${imageName}`;
      this.log(`自动生成缩略图路径: ${gameData.thumbnail}`, 'info');
    }

    // 设置默认值
    gameData.difficulty = gameData.difficulty || 'Medium';
    gameData.playerCount = gameData.playerCount || 'Single Player';
    gameData.tags = gameData.tags || gameData.genres.slice(); // 如果没有tags，使用genres作为默认值

    this.log(`游戏数据验证通过: ${gameData.name}`, 'info');
    return true;
  }

  /**
   * 验证URL格式
   * @param {string} url URL字符串
   * @returns {boolean} 是否为有效URL
   */
  static isValidUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * 清理和格式化文本
   * @param {string} text 原始文本
   * @returns {string}
   */
  static cleanText(text) {
    return text
      .replace(/[ \t]+/g, ' ')     // 多个空格和制表符合并为一个空格
      .replace(/\n\s*\n\s*\n/g, '\n\n') // 三个或更多换行合并为两个
      .trim();
  }

  /**
   * 记录操作日志
   * @param {string} message 日志消息
   * @param {string} type 日志类型 (info, success, error, warning)
   */
  static log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };
    
    console.log(`${icons[type]} [${timestamp}] ${message}`);
  }

  /**
   * 读取热门游戏配置文件
   * @returns {Array} 热门游戏配置数组
   */
  static readHotGamesConfig() {
    const configPath = path.join(process.cwd(), 'config', 'hot-games.json');
    try {
      if (!this.fileExists(configPath)) {
        this.log('热门游戏配置文件不存在，创建空配置', 'warning');
        return [];
      }
      return this.readJsonFile(configPath);
    } catch (error) {
      this.log(`读取热门游戏配置失败: ${error.message}`, 'error');
      return [];
    }
  }

  /**
   * 写入热门游戏配置文件
   * @param {Array} hotGamesConfig 热门游戏配置数组
   */
  static writeHotGamesConfig(hotGamesConfig) {
    const configPath = path.join(process.cwd(), 'config', 'hot-games.json');
    try {
      const configDir = path.dirname(configPath);
      this.ensureDirectoryExists(configDir);
      
      const content = JSON.stringify(hotGamesConfig, null, 2);
      fs.writeFileSync(configPath, content, 'utf8');
      this.log(`热门游戏配置已更新: ${configPath}`, 'success');
    } catch (error) {
      this.log(`写入热门游戏配置失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 添加游戏到热门游戏配置
   * @param {string} gameSlug 游戏slug
   * @param {Object} options 选项配置
   */
  static addToHotGamesConfig(gameSlug, options = {}) {
    try {
      const hotGamesConfig = this.readHotGamesConfig();
      
      // 检查是否已存在
      const existingIndex = hotGamesConfig.findIndex(config => config.slug === gameSlug);
      if (existingIndex !== -1) {
        this.log(`游戏已存在于热门游戏配置中: ${gameSlug}`, 'info');
        return false;
      }

      // 添加新游戏配置
      const newGameConfig = { slug: gameSlug };
      
      // 根据选项决定添加位置
      if (options.prepend) {
        hotGamesConfig.unshift(newGameConfig);
        this.log(`游戏已添加到热门游戏配置顶部: ${gameSlug}`, 'success');
      } else {
        hotGamesConfig.push(newGameConfig);
        this.log(`游戏已添加到热门游戏配置: ${gameSlug}`, 'success');
      }

      // 限制热门游戏数量（可选）
      const maxHotGames = options.maxHotGames || 20;
      if (hotGamesConfig.length > maxHotGames) {
        const removed = hotGamesConfig.splice(maxHotGames);
        this.log(`热门游戏配置已限制为${maxHotGames}个，移除了${removed.length}个旧游戏`, 'info');
      }

      // 写入配置文件
      this.writeHotGamesConfig(hotGamesConfig);
      return true;
      
    } catch (error) {
      this.log(`添加游戏到热门游戏配置失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 从热门游戏配置中移除游戏
   * @param {string} gameSlug 游戏slug
   */
  static removeFromHotGamesConfig(gameSlug) {
    try {
      const hotGamesConfig = this.readHotGamesConfig();
      
      const initialLength = hotGamesConfig.length;
      const filteredConfig = hotGamesConfig.filter(config => config.slug !== gameSlug);
      
      if (filteredConfig.length === initialLength) {
        this.log(`游戏不在热门游戏配置中: ${gameSlug}`, 'info');
        return false;
      }

      this.writeHotGamesConfig(filteredConfig);
      this.log(`游戏已从热门游戏配置中移除: ${gameSlug}`, 'success');
      return true;
      
    } catch (error) {
      this.log(`从热门游戏配置中移除游戏失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 批量更新热门游戏配置
   * @param {Array} gameSlugs 游戏slug数组
   * @param {Object} options 选项配置
   */
  static batchUpdateHotGamesConfig(gameSlugs, options = {}) {
    try {
      this.log(`开始批量更新热门游戏配置，共${gameSlugs.length}个游戏`, 'info');
      
      let addedCount = 0;
      for (const gameSlug of gameSlugs) {
        if (this.addToHotGamesConfig(gameSlug, options)) {
          addedCount++;
        }
      }
      
      this.log(`批量更新完成，成功添加${addedCount}个游戏到热门游戏配置`, 'success');
      return addedCount;
      
    } catch (error) {
      this.log(`批量更新热门游戏配置失败: ${error.message}`, 'error');
      throw error;
    }
  }
}

module.exports = Utils;
