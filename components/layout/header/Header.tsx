"use client";

import Link from "next/link";
import { Menu, Sidebar, SidebarOpen, SidebarClose } from "lucide-react";
import { useSidebar } from '@/components/layout/Sidebar';
import { siteConfig } from "@/config/site";

const Header = () => {
  const { toggleSidebar, isOpen, isMobile } = useSidebar();

  return (
    <header 
      className={`
        fixed top-0 right-0 z-[60] border-b border-border bg-background
        transition-all duration-300 ease-in-out
        ${!isMobile && isOpen ? 'left-64' : 'left-0'}
      `}
    >
      <div className="w-full px-4">
        <nav className="relative flex items-center h-16">
          {/* 左侧 - 侧边栏切换按钮 */}
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="flex items-center space-x-2 px-3 py-2 rounded hover:bg-accent transition-colors group"
              aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
              title={isOpen ? "Close sidebar" : "Open sidebar"}
            >
              {isMobile ? (
                // 移动端使用汉堡菜单图标
                <>
                  <Menu className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                  <span className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                    Menu
                  </span>
                </>
              ) : (
                // 桌面端使用侧边栏图标，根据状态显示不同图标
                <>
                  {isOpen ? (
                    <>
                      <SidebarClose className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                      <span className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                        Hide
                      </span>
                    </>
                  ) : (
                    <>
                      <SidebarOpen className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                      <span className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                        Menu
                      </span>
                    </>
                  )}
                </>
              )}
            </button>
          </div>

          {/* 中央Logo */}
          <div className="flex-1 flex justify-center">
            <Link href="/" className="flex items-center hover:scale-105 transition-transform duration-200">
              <span className="text-2xl font-bold text-foreground">{siteConfig.name}</span>
            </Link>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;