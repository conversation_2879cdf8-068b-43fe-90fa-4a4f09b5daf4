"use client";

import AdSenseAd from '@/components/common/ads/AdSenseAd';

interface SidebarAdProps {
  className?: string;
}

/**
 * 侧边栏广告组件 - 适用于侧边栏或文章内容旁边
 * 使用矩形格式，适合较窄的空间
 */
const SidebarAd: React.FC<SidebarAdProps> = ({ 
  className = "" 
}) => {
  const adSlot = process.env.NEXT_PUBLIC_AD_SLOT_SIDEBAR;

  if (!adSlot) {
    return null;
  }

  return (
    <div className={`w-full flex justify-center my-4 ${className}`}>
      <AdSenseAd
        adSlot={adSlot}
        adFormat="rectangle"
        isResponsive={true}
        style={{
          width: "300px",
          height: "250px",
        }}
        className="sidebar-ad bg-card border-border"
      />
    </div>
  );
};

export default SidebarAd; 