:root {
  --background: oklch(0.9784 0.0059 59.6529);
  --foreground: oklch(0.2178 0 0);
  --card: oklch(0.9784 0.0059 59.6529);
  --card-foreground: oklch(0.2178 0 0);
  --popover: oklch(0.9784 0.0059 59.6529);
  --popover-foreground: oklch(0.2178 0 0);
  --primary: oklch(0.4864 0.1870 27.8841);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9656 0.0424 89.3551);
  --secondary-foreground: oklch(0.4847 0.1022 75.1153);
  --muted: oklch(0.9441 0.0101 58.2129);
  --muted-foreground: oklch(0.4458 0.0137 71.7155);
  --accent: oklch(0.9645 0.0604 96.1410);
  --accent-foreground: oklch(0.4205 0.1668 28.5048);
  --destructive: oklch(0.4802 0.1961 29.1224);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9435 0.0451 81.3276);
  --input: oklch(0.9435 0.0451 81.3276);
  --ring: oklch(0.4864 0.1870 27.8841);
  --chart-1: oklch(0.5482 0.2250 29.2339);
  --chart-2: oklch(0.4864 0.1870 27.8841);
  --chart-3: oklch(0.4205 0.1668 28.5048);
  --chart-4: oklch(0.5673 0.1563 47.2384);
  --chart-5: oklch(0.4910 0.1442 43.4774);
  --sidebar: oklch(0.9441 0.0101 58.2129);
  --sidebar-foreground: oklch(0.2178 0 0);
  --sidebar-primary: oklch(0.4864 0.1870 27.8841);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9645 0.0604 96.1410);
  --sidebar-accent-foreground: oklch(0.4205 0.1668 28.5048);
  --sidebar-border: oklch(0.9435 0.0451 81.3276);
  --sidebar-ring: oklch(0.4864 0.1870 27.8841);
  --font-sans: Poppins, sans-serif;
  --font-serif: Libre Baskerville, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.06);
  --shadow-xs: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.06);
  --shadow-sm: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 1px 2px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 1px 2px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-md: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 2px 4px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-lg: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 4px 6px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-xl: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 8px 10px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-2xl: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.30);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2170 0.0086 59.1838);
  --foreground: oklch(0.9699 0.0013 106.4238);
  --card: oklch(0.2667 0.0093 28.9881);
  --card-foreground: oklch(0.9699 0.0013 106.4238);
  --popover: oklch(0.2667 0.0093 28.9881);
  --popover-foreground: oklch(0.9699 0.0013 106.4238);
  --primary: oklch(0.5482 0.2250 29.2339);
  --primary-foreground: oklch(0.9784 0.0059 59.6529);
  --secondary: oklch(0.4910 0.1442 43.4774);
  --secondary-foreground: oklch(0.9645 0.0604 96.1410);
  --muted: oklch(0.2667 0.0093 28.9881);
  --muted-foreground: oklch(0.8692 0.0060 59.6397);
  --accent: oklch(0.5673 0.1563 47.2384);
  --accent-foreground: oklch(0.9645 0.0604 96.1410);
  --destructive: oklch(0.6496 0.2362 26.9032);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3755 0.0130 67.4155);
  --input: oklch(0.3755 0.0130 67.4155);
  --ring: oklch(0.5482 0.2250 29.2339);
  --chart-1: oklch(0.7102 0.1824 22.9075);
  --chart-2: oklch(0.6496 0.2362 26.9032);
  --chart-3: oklch(0.6286 0.2571 29.1592);
  --chart-4: oklch(0.8447 0.1675 84.1157);
  --chart-5: oklch(0.7857 0.1716 68.5720);
  --sidebar: oklch(0.2170 0.0086 59.1838);
  --sidebar-foreground: oklch(0.9699 0.0013 106.4238);
  --sidebar-primary: oklch(0.5482 0.2250 29.2339);
  --sidebar-primary-foreground: oklch(0.9784 0.0059 59.6529);
  --sidebar-accent: oklch(0.5673 0.1563 47.2384);
  --sidebar-accent-foreground: oklch(0.9645 0.0604 96.1410);
  --sidebar-border: oklch(0.3755 0.0130 67.4155);
  --sidebar-ring: oklch(0.5482 0.2250 29.2339);
  --font-sans: Poppins, sans-serif;
  --font-serif: Libre Baskerville, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.06);
  --shadow-xs: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.06);
  --shadow-sm: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 1px 2px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 1px 2px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-md: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 2px 4px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-lg: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 4px 6px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-xl: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.12), 1px 8px 10px -3px hsl(0 89.0110% 17.8431% / 0.12);
  --shadow-2xl: 1px 1px 16px -2px hsl(0 89.0110% 17.8431% / 0.30);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-2xl: calc(var(--radius) + 8px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}