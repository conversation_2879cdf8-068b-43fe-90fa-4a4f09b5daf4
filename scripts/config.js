require('dotenv').config({ path: '.env.local' });

// GLM-4-Flash API 配置
const AI_CONFIG = {
  // 智谱AI API配置
  ZHIPU: {
    baseURL: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4-flash',
    apiKey: process.env.ZHIPU_API_KEY,
    maxTokens: 1500,
    temperature: 0.7,
    // 请求超时时间（毫秒）
    timeout: 30000,
    // 重试配置
    retryAttempts: 3,
    retryDelay: 1000
  }
};

// 内容生成配置
const CONTENT_CONFIG = {
  // 内容生成配置
  language: {
    primary: 'english',
    level: 'grade6', // 六年级英语水平
    maxWordsPerSentence: 15,
    preferredWords: ['fun', 'easy', 'play', 'game', 'free', 'online', 'cool', 'great', 'simple', 'enjoy']
  },
  
  // SEO优化配置
  seo: {
    keywordDensity: {
      min: 3.0,    // 最低关键词密度 3%
      max: 4.5,    // 最高关键词密度 4.5%
      target: 3.5, // 目标关键词密度 3.5%
      tolerance: 0.2 // 允许的误差范围
    },
    titleFormat: 'gameName', // 直接使用游戏名称作为标题
    metaDescriptionLength: {
      min: 140,
      max: 155
    },
    requiredKeywords: ['free', 'online', 'play', 'game'],
    supportKeywords: ['fun', 'browser', 'no download', 'instant']
  },
  
  // 内容长度配置
  contentLimits: {
    introduction: { min: 150, max: 250 },
    features: { min: 200, max: 300 },
    howToPlay: { min: 100, max: 200 },
    controls: { min: 80, max: 150 },
    tips: { min: 120, max: 200 },
    similarGames: { min: 60, max: 120 }
  },
  
  // 生成内容的基本参数
  maxTitleLength: 60,
  maxDescriptionLength: 160,
  minContentLength: 500,
  
  // 支持的游戏类型
  supportedGenres: [
    'RPG', 'Adventure', 'Action', 'Strategy', 'Simulation', 
    'Sports', 'Racing', 'Puzzle', 'Fighting', 'Shooter'
  ],
  
  // 默认标签
  defaultTags: ['Online Game', 'Free to Play', 'Browser Game'],
  
  // 文件路径配置
  paths: {
    gamesDir: './content/games/',
    dataDir: 'scripts/data',
    templatesDir: 'scripts/templates',
    outputDir: 'content/games',
    thumbnailsDir: '/images/thumbnails'
  },
  
  // 质量控制配置
  quality: {
    minScore: 75,           // 最低质量分数
    readabilityScore: 80,   // 可读性分数要求
    maxRetries: 2,          // 内容生成最大重试次数
    enableStrictMode: true  // 启用严格模式
  }
};

// 导出配置
module.exports = {
  AI_CONFIG,
  CONTENT_CONFIG
};
