import React from 'react';

interface StructuredDataProps {
  data: any | any[];
}

/**
 * 结构化数据组件
 * 用于在页面中渲染JSON-LD结构化数据
 */
export default function StructuredData({ data }: StructuredDataProps) {
  // 如果数据是数组，渲染多个script标签
  if (Array.isArray(data)) {
    return (
      <>
        {data.map((schema, index) => (
          <script
            key={index}
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(schema, null, 0),
            }}
          />
        ))}
      </>
    );
  }

  // 单个结构化数据对象
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data, null, 0),
      }}
    />
  );
}

 