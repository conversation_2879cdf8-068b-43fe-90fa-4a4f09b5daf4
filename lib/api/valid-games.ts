import fs from 'fs';
import path from 'path';

// 游戏内容目录路径
const gamesDirectory = path.join(process.cwd(), 'content/games');

/**
 * 获取所有有效游戏的slug列表
 * 这个函数会读取content/games目录下的所有.md文件，并返回它们的slug
 */
export function getValidGameSlugs(): string[] {
  try {
    // 确保目录存在
    if (!fs.existsSync(gamesDirectory)) {
      return [];
    }

    const fileNames = fs.readdirSync(gamesDirectory);
    return fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => fileName.replace(/\.md$/, ''));
  } catch (error) {
    console.error('Error reading valid game slugs:', error);
    return [];
  }
}

/**
 * 检查游戏slug是否有效
 * @param slug 游戏slug
 * @returns 如果游戏存在，返回true；否则返回false
 */
export function isValidGameSlug(slug: string): boolean {
  try {
    const fullPath = path.join(gamesDirectory, `${slug}.md`);
    return fs.existsSync(fullPath);
  } catch (error) {
    return false;
  }
}