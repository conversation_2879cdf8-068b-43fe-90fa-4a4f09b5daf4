"use client";

import React from 'react';

interface ChartExampleProps {
  /** 图表标题 */
  title?: string;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 简单的图表示例组件，展示如何使用主题中的图表颜色变量
 */
export default function ChartExample({ title = '图表示例', className = '' }: ChartExampleProps) {
  return (
    <div className={`p-4 theme-card ${className}`}>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      
      <div className="flex items-end h-40 gap-2">
        {/* 使用chart变量创建简单的柱状图 */}
        <div 
          className="w-1/5 bg-chart-1 rounded-t-md" 
          style={{ height: '60%' }}
          title="Chart 1"
        />
        <div 
          className="w-1/5 bg-chart-2 rounded-t-md" 
          style={{ height: '80%' }}
          title="Chart 2"
        />
        <div 
          className="w-1/5 bg-chart-3 rounded-t-md" 
          style={{ height: '40%' }}
          title="Chart 3"
        />
        <div 
          className="w-1/5 bg-chart-4 rounded-t-md" 
          style={{ height: '70%' }}
          title="Chart 4"
        />
        <div 
          className="w-1/5 bg-chart-5 rounded-t-md" 
          style={{ height: '90%' }}
          title="Chart 5"
        />
      </div>
      
      <div className="flex justify-between mt-2 text-xs text-muted-foreground">
        <span>数据1</span>
        <span>数据2</span>
        <span>数据3</span>
        <span>数据4</span>
        <span>数据5</span>
      </div>
      
      <div className="mt-4 text-sm text-muted-foreground">
        此图表使用了theme.css中定义的chart-1到chart-5变量
      </div>
    </div>
  );
}