"use client";

import AdSenseAd from '@/components/common/ads/AdSenseAd';

interface InContentAdProps {
  className?: string;
}

/**
 * 内容内广告组件 - 适用于文章内容中间
 * 使用响应式设计，能够很好地融入内容流
 */
const InContentAd: React.FC<InContentAdProps> = ({ 
  className = "" 
}) => {
  const adSlot = process.env.NEXT_PUBLIC_AD_SLOT_IN_CONTENT;

  if (!adSlot) {
    return null;
  }
  
  return (
    <div className={`w-full flex justify-center my-6 py-4 ${className}`}>
      <div className="w-full max-w-2xl">
        <div className="text-xs text-muted-foreground text-center mb-2">Advertisement</div>
        <AdSenseAd
          adSlot={adSlot}
          adFormat="auto"
          isResponsive={true}
          style={{
            width: "100%",
            minHeight: "250px",
          }}
          className="in-content-ad bg-card border-border"
        />
      </div>
    </div>
  );
};

export default InContentAd; 