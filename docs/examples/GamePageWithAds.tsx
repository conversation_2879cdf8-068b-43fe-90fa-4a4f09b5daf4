/**
 * 游戏页面广告集成示例
 * 这个文件展示了如何在您的游戏页面中集成 AdSense 广告
 * 
 * 使用方法：
 * 1. 在 AdSense 控制台中创建对应的广告位
 * 2. 将示例中的广告位 ID 替换为您的实际 ID
 * 3. 根据需要调整广告的位置和样式
 */

import BannerAd from "@/components/ads/BannerAd";
import SidebarAd from "@/components/ads/SidebarAd";
import InContentAd from "@/components/ads/InContentAd";

interface GamePageWithAdsProps {
  children: React.ReactNode;
}

const GamePageWithAdsExample: React.FC<GamePageWithAdsProps> = ({ children }) => {
  return (
    <div className="game-page-container">
      {/* 页面顶部横幅广告 */}
      <BannerAd 
        className="mb-6"
      />
      
      <div className="flex flex-col lg:flex-row gap-6">
        {/* 左侧边栏广告 */}
        <aside className="lg:w-64 order-3 lg:order-1">
          <div className="sticky top-4">
            <SidebarAd 
              className="mb-4"
            />
            {/* 其他侧边栏内容 */}
          </div>
        </aside>
        
        {/* 主要内容区域 */}
        <main className="flex-1 order-1 lg:order-2">
          {/* 游戏内容的前半部分 */}
          <div className="game-content-top">
            {children}
          </div>
          
          {/* 内容中间的广告 */}
          <InContentAd 
            className="my-8"
          />
          
          {/* 游戏内容的后半部分 */}
          <div className="game-content-bottom">
            {/* 更多游戏内容 */}
          </div>
        </main>
        
        {/* 右侧边栏广告 */}
        <aside className="lg:w-64 order-2 lg:order-3">
          <div className="sticky top-4">
            <SidebarAd 
              className="mb-4"
            />
            {/* 其他侧边栏内容 */}
          </div>
        </aside>
      </div>
      
      {/* 页面底部横幅广告 */}
      <BannerAd 
        className="mt-6"
      />
    </div>
  );
};

export default GamePageWithAdsExample; 