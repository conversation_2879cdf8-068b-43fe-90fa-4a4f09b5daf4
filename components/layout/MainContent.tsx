"use client";

import { useSidebar } from '@/components/layout/Sidebar';

interface MainContentProps {
  children: React.ReactNode;
}

const MainContent: React.FC<MainContentProps> = ({ children }) => {
  const { isOpen, isMobile } = useSidebar();
  
  return (
    <main 
      className={`
        min-h-screen pt-16 transition-all duration-300 ease-in-out
        ${!isMobile && isOpen ? 'ml-64' : 'ml-0'}
      `}
    >
      {children}
    </main>
  );
};

export default MainContent; 