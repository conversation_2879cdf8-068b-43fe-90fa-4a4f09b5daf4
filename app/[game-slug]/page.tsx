import GamePlayer from '@/components/features/game/GamePlayer';
import GameContentSection from '@/components/features/game/GameContentSection';
import RelatedGamesCarousel from '@/components/features/game/RelatedGamesCarousel';

import StructuredData from '@/components/common/StructuredData';
import { getGameBySlug, getGameSlugs, getRelatedGameFrontmatters, getAllGameFrontmatters, getHotGameFrontmatters } from '@/lib/api/markdown';
import { generateVideoGameSchema, combineStructuredData } from '@/lib/constants/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { cache } from 'react';

interface GamePageProps {
  params: Promise<{
    'game-slug': string;
  }>;
}

// 缓存游戏数据获取函数
const getCachedGameData = cache(async (slug: string) => {
  try {
    return await getGameBySlug(slug);
  } catch (error) {
    console.error('Error fetching game data:', error);
    return null;
  }
});

// 智能游戏列表合并函数（与首页保持一致）
function createSmartGameList(newGames: any[], hotGames: any[], excludeGameSlug: string) {
  const finalList: any[] = [];
  let newGameIndex = 0;
  let hotGameIndex = 0;
  
  // 过滤掉当前游戏
  const filteredNewGames = newGames.filter(g => g.slug !== excludeGameSlug);
  const filteredHotGames = hotGames.filter(g => g.slug !== excludeGameSlug);
  
  // 智能合并：2个新游戏 + 1个热门游戏的模式
  while (finalList.length < 24) {
    // 添加2个新游戏
    for (let i = 0; i < 2 && newGameIndex < filteredNewGames.length && finalList.length < 24; i++) {
      const game = filteredNewGames[newGameIndex];
      if (game && !finalList.some(g => g.slug === game.slug)) {
        finalList.push(game);
      }
      newGameIndex++;
    }
    
    // 添加1个热门游戏
    if (hotGameIndex < filteredHotGames.length && finalList.length < 24) {
      const game = filteredHotGames[hotGameIndex];
      if (game && !finalList.some(g => g.slug === game.slug)) {
        finalList.push(game);
      }
      hotGameIndex++;
    }
    
    // 如果所有游戏都用完了，提前结束
    if (finalList.length >= 24 || (newGameIndex >= filteredNewGames.length && hotGameIndex >= filteredHotGames.length)) {
      break;
    }
  }
  
  return finalList;
}

// 缓存相关数据获取函数
const getCachedRelatedData = cache(async (gameSlug: string) => {
  try {
    const game = await getCachedGameData(gameSlug);
    if (!game) {
      return {
        relatedGames: [],
        leftGames: [],
        rightGames: []
      };
    }

    const [relatedGames, newGames, hotGames] = await Promise.all([
      getRelatedGameFrontmatters(game.slug, 24), // 增加到24个相关游戏用于轮播
      getAllGameFrontmatters({ sortByDate: true }).then(games => games.slice(0, 20)),
      getHotGameFrontmatters().then(games => games.slice(0, 15))
    ]);
    
    // 创建智能合并的侧边栏游戏列表（与首页保持一致）
    const smartGameList = createSmartGameList(newGames, hotGames, gameSlug);
    
    // 分配给左右侧边栏 - 调整为2×6布局，每个侧边栏12个游戏
    const leftGames = smartGameList.slice(0, 12);  // 取前12个给左侧边栏
    const rightGames = smartGameList.slice(12, 24); // 取第13-24个给右侧边栏
    
    return { relatedGames, leftGames, rightGames };
  } catch (error) {
    console.error('Error fetching related data:', error);
    return {
      relatedGames: [],
      leftGames: [],
      rightGames: []
    };
  }
});

// Generate static params for all games
export async function generateStaticParams() {
  const slugs = getGameSlugs();
  
  return slugs.map((slug) => ({
    'game-slug': slug,
  }));
}

// Generate dynamic metadata for SEO
export async function generateMetadata({ params }: GamePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const gameSlug = resolvedParams['game-slug'];
  const game = await getCachedGameData(gameSlug);
  
  if (!game) {
    return {
      title: 'Game Not Found',
      description: 'The requested game could not be found.',
    };
  }

  return {
    title: game.title,
    description: game.description,
    authors: game.author ? [{ name: game.author }] : undefined,
    openGraph: {
      title: game.title,
      description: game.description,
      type: 'website',
      url: `/${gameSlug}`,
      images: [
        {
          url: game.thumbnail,
          width: 1200,
          height: 630,
          alt: game.title,
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
    },
    twitter: {
      card: 'summary_large_image',
      title: game.title,
      description: game.description,
      images: [game.thumbnail],
    },
    alternates: {
      canonical: `/${gameSlug}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function GamePage({ params }: GamePageProps) {
  const resolvedParams = await params;
  const gameSlug = resolvedParams['game-slug'];
  
  // 使用缓存函数获取游戏数据
  const game = await getCachedGameData(gameSlug);
  
  // If game is not found, show 404 page
  if (!game) {
    notFound();
  }

  // 使用缓存函数获取相关数据
  const { relatedGames, leftGames, rightGames } = await getCachedRelatedData(gameSlug);

  // 生成结构化数据
  const gameUrl = `${siteConfig.url}/${gameSlug}`;
  const gameSchema = generateVideoGameSchema({
    title: game.title,
    slug: game.slug,
    description: game.description,
    thumbnail: game.thumbnail,
    genres: game.genres,
    publishedDate: game.publishedDate,
    author: game.author,
    version: game.version,
    iframeUrl: game.iframeUrl,
    pageDescription: game.pageDescription,
  }, gameUrl);

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={gameSchema} />
      
      <div className="min-h-screen bg-background">
        
        {/* 主内容区域 - 全宽布局，响应侧边栏状态 */}
        <div className="w-full px-2 sm:px-4 lg:px-6 py-4">
          {/* Main game area - 方案A：扩大的GamePlayer容器 */}
          <div className="mb-6 flex justify-center">
            <div className="w-full max-w-[1800px]">
              <GamePlayer
                title={game.title}
                displayTitle={game.displayTitle}
                thumbnail={game.thumbnail}
                iframeUrl={game.iframeUrl}
                gameSlug={game.slug}
                gameData={{
                  title: game.title,
                  slug: game.slug,
                  thumbnail: game.thumbnail,
                  genres: game.genres,
                  description: game.description,
                  iframeUrl: game.iframeUrl,
                  publishedDate: game.publishedDate,
                  author: game.author,
                  version: game.version,
                  pageDescription: game.pageDescription
                }}
                leftGames={leftGames}
                rightGames={rightGames}
                className="w-full"
              />
            </div>
          </div>

          {/* Bottom carousel section */}
          <div className="mb-6 flex justify-center">
            <div className="w-full max-w-[1800px] theme-card-elevated p-4 lg:p-8 transition-shadow duration-300 hover:shadow-xl">
              <RelatedGamesCarousel
                games={relatedGames}
                title="Related Games"
                autoPlay={true}
                autoPlayInterval={4000}
                cardSize="medium"
                showTags={true}
              />
            </div>
          </div>

          {/* 底部区域：左侧内容说明 + 右侧留言板 */}
          <div className="flex justify-center">
            <div className="w-full max-w-[1800px]">
              {/* 游戏内容说明描述区 */}
              <div className="w-full">
                <div className="theme-card">
                  <GameContentSection 
                    game={game}
                    showGameMeta={true}
                    className="p-0" // 移除默认padding，因为容器已有
                    contentClassName="prose prose-sm lg:prose-lg max-w-none"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// ISR配置 - 增量静态再生
export const revalidate = 3600; // 1小时重新验证

// 强制静态渲染
export const dynamic = 'force-static';

// 运行时配置
export const runtime = 'nodejs';