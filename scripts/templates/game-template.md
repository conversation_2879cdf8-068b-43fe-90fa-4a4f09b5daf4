---
# 模板说明:
# 这是一个用于添加新游戏内容的标准模板。
# 请复制此文件，重命名为您游戏的 slug (例如 "my-cool-game.md")，并将其放置在 /games/ 目录下。
# 然后，仔细填写下面的所有字段。

# --- 游戏核心信息 (必填) ---

# title: 游戏的完整名称，将显示在页面标题和H1标签中。
# 示例: "Pokemon Cool Version"
title: ""

# slug: 游戏的URL友好名称，将用于构建页面链接。
# 规则: 只能使用小写字母、数字和连字符(-)。
# 示例: "pokemon-cool-version"
slug: ""

# thumbnail: 游戏在列表页和分享卡片中显示的缩略图。
# 路径: 必须指向 /public/images/thumbnails/ 目录下的一个图片文件。
# 示例: "/images/thumbnails/pokemon-cool-version.png"
thumbnail: ""

# genres: 游戏所属的分类列表。
# 规则: 这是一个数组，可以包含一个或多个分类。这些分类应与 config/core-genres.json 中定义的相匹配。
# 示例: ["RPG", "Adventure"]
genres: []

# iframeUrl: 用于嵌入游戏的可玩链接。
# 来源: 这通常是来自其他游戏站点的嵌入式播放器URL。
# 示例: "https://arcadespot.com/game/pokemon-cool-version/"
iframeUrl: ""


# --- SEO 与页面内容 (推荐填写) ---

# description: 游戏的简短描述，将用于游戏列表页和搜索引擎结果的摘要。
# 长度: 建议在 150-160 个字符以内。
# 示例: "Embark on a new journey in Pokemon Cool Version, featuring new regions and over 100 new creatures to discover."
description: ""

# pageDescription: 游戏详情页专用的 Meta Description。
# 作用: 如果不填写，将默认使用上面的 `description`。填写此字段可以为详情页提供更具吸引力的搜索引擎摘要。
# 长度: 建议在 150-160 个字符以内。
# 示例: "Play Pokemon Cool Version online for free! Explore the Kanto region like never before, discover unique features, and challenge powerful new gym leaders. No download required."
pageDescription: ""


# --- 元数据 (可选) ---

# publishedDate: 游戏的发布或上线日期。
# 格式: YYYY-MM-DD
# 示例: "2025-07-01"
publishedDate: ""
---

# 在这里开始撰写游戏的主要内容

欢迎来到 [在此处填写游戏名称]!

在这里，您可以添加关于游戏的详细介绍、玩法说明、游戏特色、截图等。
这部分内容将显示在 `<iframe>` 播放器下方。

## 玩法说明
1.  ...
2.  ...

## 游戏特色
-   **特色一**: ...
-   **特色二**: ...

![在此处填写图片描述](/images/thumbnails/在此处替换为游戏截图.png) 