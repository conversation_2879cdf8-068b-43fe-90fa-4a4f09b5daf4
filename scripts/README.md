# 游戏页面生成器 - 增强版

这是一个专为创建游戏详情页面而设计的自动化工具，支持AI内容生成、SEO优化和内容质量控制。

## 🚀 主要特性

### ✨ 新优化功能
- **六年级英语水平内容生成** - 使用简单易懂的英文创建页面内容
- **严格SEO控制** - 关键词密度精确控制在3-4.5%范围内
- **标准化输入格式** - 支持5个核心字段的简化输入
- **自动图片路径生成** - 根据游戏名称自动生成缩略图路径
- **英文可读性检查** - 确保内容符合目标用户阅读水平

### 🎯 核心功能
- **AI内容生成** - 使用智谱AI GLM-4-Flash生成高质量英文内容
- **内容质量检查** - 多维度质量评估和优化建议
- **批量处理** - 支持批量生成多个游戏页面
- **模板系统** - 灵活的Markdown模板支持
- **日志记录** - 详细的操作日志和错误追踪

## 📋 输入格式要求

### 标准化输入字段
每个游戏只需要提供以下4个核心字段：

```json
{
  "name": "游戏名称",
  "iframeUrl": "游戏的iframe链接",
  "genres": ["游戏类型标签1", "游戏类型标签2"],
  "gameDescription": "游戏说明文本，用作AI生成参考"
}
```

### 字段说明
- **name** - 游戏名称，将用作页面H1标题和SEO标题
- **iframeUrl** - 游戏的iframe嵌入链接
- **genres** - 游戏类型标签数组（如：RPG、Adventure、Action等）
- **gameDescription** - 游戏说明，为AI生成内容提供参考信息

### 自动生成字段
- **thumbnail** - 缩略图路径自动生成为：`/images/thumbnails/{游戏名称slug}.png`
- **difficulty** - 游戏难度，默认为"Medium"
- **playerCount** - 玩家数量，默认为"Single Player"
- **tags** - 如未提供，将使用genres作为默认标签

## 🛠️ 安装和配置

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
创建 `.env.local` 文件并添加AI服务配置：
```env
ZHIPU_API_KEY=your_zhipu_api_key_here
```

### 3. 准备数据文件
在 `scripts/data/new-games.json` 中添加游戏数据：
```json
[
  {
    "name": "Pokemon Gamma Emerald",
    "iframeUrl": "https://example.com/games/pokemon-gamma-emerald",
    "genres": ["RPG", "Adventure"],
    "gameDescription": "A fan-made Pokemon game based on Emerald version. Features new region, updated mechanics, and enhanced graphics. Players catch and train Pokemon while exploring dungeons and battling gym leaders."
  }
]
```

## 🚀 使用方法

### 基本使用
```bash
# 运行增强版生成器
node scripts/create-games-enhanced.js

# 测试功能
node scripts/test-enhanced-generator.js
```

### 高级选项
```javascript
const generator = new EnhancedGameGenerator({
  useAI: true,                    // 启用AI生成
  enableQualityCheck: true,       // 启用质量检查
  minQualityScore: 75,           // 最低质量分数
  batchSize: 3,                  // 批处理大小
  delayBetweenBatches: 2000,     // 批次间延迟
  generateReport: true           // 生成详细报告
});
```

## 📊 内容质量标准

### SEO优化要求
- **关键词密度**: 3.0% - 4.5%（严格控制）
- **标题格式**: 直接使用游戏名称
- **描述长度**: 140-155字符
- **必需关键词**: free, online, play, game

### 英文水平要求
- **目标水平**: 六年级英语（Grade 6）
- **句子长度**: 最多15个单词
- **可读性分数**: ≥80分（Flesch Reading Ease）
- **词汇复杂度**: 简单常用词汇优先

### 内容结构要求
- **介绍部分**: 150-250字符
- **特色功能**: 4-6个要点，包含emoji图标
- **玩法说明**: 4-6个简单步骤
- **控制说明**: 简洁的按键说明
- **游戏技巧**: 4-6个实用建议

## 📁 文件结构

```
scripts/
├── ai-service.js              # AI内容生成服务
├── batch-processor.js         # 批处理器
├── config.js                  # 配置文件
├── content-quality.js         # 内容质量检查器
├── create-games-enhanced.js   # 主生成器
├── logger.js                  # 日志系统
├── test-enhanced-generator.js # 测试脚本
├── utils.js                   # 工具函数
├── data/
│   └── new-games.json        # 游戏数据文件
├── templates/
│   └── game-template.md      # 页面模板
└── logs/                     # 日志文件目录
```

## 🔍 质量检查功能

脚本会自动检查以下质量指标：

### 关键词密度检查
- 主要关键词（游戏名称、类型）: 3.0-4.5%
- 辅助关键词（free, online, play等）: ≤3.0%
- 超出范围会提供具体的调整建议

### 英文可读性检查
- 句子长度分析
- 词汇复杂度评估
- Flesch Reading Ease分数计算
- 六年级英语水平验证

### 内容结构检查
- 各部分长度验证
- 必需部分完整性检查
- 格式规范性验证

## 📈 使用示例

### 输入示例
```json
{
  "name": "Super Mario Adventure",
  "iframeUrl": "https://games.example.com/mario-adventure",
  "genres": ["Platform", "Adventure"],
  "gameDescription": "A classic platform game where Mario jumps and runs through colorful levels. Players collect coins, power-ups, and defeat enemies while exploring different worlds. The game features simple controls and fun gameplay for all ages."
}
```

### 输出页面特点
- ✅ 标题：直接使用"Super Mario Adventure"
- ✅ 描述：包含关键词，长度符合要求
- ✅ 内容：六年级英语水平，简单易懂
- ✅ SEO：关键词密度3-4.5%
- ✅ 图片：自动生成路径`/images/thumbnails/super-mario-adventure.png`

## 🔧 自动配置功能

### 热门游戏自动配置
脚本现在支持自动将新生成的游戏添加到热门游戏配置中：

- ✅ **自动添加** - 游戏生成成功后自动添加到 `config/hot-games.json`
- ✅ **智能排序** - 新游戏自动添加到列表顶部
- ✅ **重复检测** - 防止重复添加同一游戏
- ✅ **数量限制** - 自动限制热门游戏数量（默认20个）
- ✅ **错误处理** - 配置更新失败不影响主流程

### 配置管理工具函数
```javascript
// 添加游戏到热门配置
Utils.addToHotGamesConfig('game-slug', { 
  prepend: true,     // 添加到顶部
  maxHotGames: 20    // 限制数量
});

// 移除游戏
Utils.removeFromHotGamesConfig('game-slug');

// 批量添加
Utils.batchUpdateHotGamesConfig(['slug1', 'slug2'], options);
```

### 测试自动配置
```bash
# 运行配置功能测试
node scripts/test-auto-config.js
```

## 🐛 故障排除

### 常见问题
1. **AI生成失败** - 检查API密钥配置和网络连接
2. **质量分数过低** - 查看具体问题和建议进行调整
3. **关键词密度超标** - AI会自动重试生成符合要求的内容
4. **游戏未显示在网站** - 检查 `games/*.md` 文件格式和 `config/hot-games.json` 配置
4. **数据验证失败** - 确保提供所有必需字段

### 调试模式
```bash
# 启用详细日志
node scripts/create-games-enhanced.js --logLevel=debug

# 运行测试验证功能
node scripts/test-enhanced-generator.js
```

## 📝 更新日志

### v2.0.0 - 英文优化版
- ✨ 新增六年级英语水平内容生成
- ✨ 严格关键词密度控制（3-4.5%）
- ✨ 标准化输入格式支持
- ✨ 自动图片路径生成
- ✨ 英文可读性检查功能
- 🔧 优化AI提示词模板
- 🔧 简化页面标题为游戏名称
- 🔧 增强内容质量检查器

---

## 📞 技术支持

如需帮助或有问题，请查看日志文件或运行测试脚本进行诊断。 