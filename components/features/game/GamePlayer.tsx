"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Maximize, Play, Pause, Volume2, VolumeX, PlayCircle } from 'lucide-react';
import { GameFrontmatter } from '@/lib/types';
import ShareDropdown from '@/components/layout/ShareDropdown';
import BookmarkButton from '@/components/common/ui/BookmarkButton';
import GameCard from '@/components/features/game/GameCard';
import Image from 'next/image';
import { generateGameCoverAlt } from '@/lib/utils/alt-text-utils';

interface GamePlayerProps {
  /** Game title, displayed in the bottom extension bar */
  title: string;
  /** Display title from H1 in markdown content */
  displayTitle?: string | null;
  /** Game thumbnail for play button background */
  thumbnail?: string;
  /** iframe embed URL */
  iframeUrl: string;
  /** Game slug for favorites and sharing functionality */
  gameSlug?: string;
  /** Game data for favorites functionality */
  gameData?: GameFrontmatter;
  /** Left side games (reserved for future use) */
  leftGames?: GameFrontmatter[];
  /** Right side games (reserved for future use) */
  rightGames?: GameFrontmatter[];
  /** Custom CSS class name */
  className?: string;
  /** Whether to show play control buttons */
  showPlayControls?: boolean;
  /** Favorite status callback */
  onFavorite?: (isFavorited: boolean) => void;
  /** Share callback */
  onShare?: () => void;
  /** Fullscreen callback */
  onFullscreen?: () => void;
}

export default function GamePlayer({
  title,
  displayTitle,
  thumbnail,
  iframeUrl,
  gameSlug,
  gameData,
  leftGames = [],
  rightGames = [],
  className = '',
  showPlayControls = false,
  onFavorite,
  onShare,
  onFullscreen
}: GamePlayerProps) {
  const [isPlaying, setIsPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showIframe, setShowIframe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const playerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 获取显示标题，优先使用displayTitle，回退到title
  const getDisplayTitle = () => {
    return displayTitle || title;
  };

  // Handle play button click
  const handlePlayClick = () => {
    setIsLoading(true);
    setShowIframe(true);
    // 模拟加载时间
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Handle fullscreen functionality
  const handleFullscreen = () => {
    if (!playerRef.current) return;

    if (!isFullscreen) {
      // Enter fullscreen
      if (playerRef.current.requestFullscreen) {
        playerRef.current.requestFullscreen();
      } else if ((playerRef.current as any).webkitRequestFullscreen) {
        (playerRef.current as any).webkitRequestFullscreen();
      } else if ((playerRef.current as any).msRequestFullscreen) {
        (playerRef.current as any).msRequestFullscreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
    onFullscreen?.();
  };

  // Listen for fullscreen state changes
  const handleFullscreenChange = () => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );
    setIsFullscreen(isCurrentlyFullscreen);
  };

  // Add fullscreen event listeners
  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, []);

  if (!iframeUrl) {
    return (
      <div className={`theme-card rounded-lg overflow-hidden ${className}`}>
        {/* Game area placeholder */}
        <div className="aspect-video flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-4">🎮</div>
            <p className="theme-text-muted">
              Game content not available
            </p>
          </div>
        </div>
        
        {/* Bottom extension bar */}
        <div className="theme-card border-t theme-border p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold text-foreground truncate flex-1 mr-4">
              {getDisplayTitle()}
            </h1>
            <div className="flex items-center gap-2">
              <span className="text-sm theme-text-muted">Game not available</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 判断是否显示侧边栏
  const showSidebars = leftGames.length > 0 || rightGames.length > 0;

  return (
    <div 
      ref={playerRef}
      className={`${
        isFullscreen 
          ? 'fixed inset-0 z-50 bg-background' 
          // 新的 Flexbox 布局 - 添加内边距以匹配轮播组件
          : 'flex justify-center gap-x-4 w-full px-2' 
      } ${className}`}
    >
      {isFullscreen ? (
        /* 全屏模式：简单的iframe填充 */
        showIframe ? (
          <iframe
            ref={iframeRef}
            src={iframeUrl}
            title={`Play ${getDisplayTitle()}`}
            className="w-full h-full border-0"
            allowFullScreen
            loading="lazy"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          />
        ) : (
          <div className="w-full h-full bg-background flex items-center justify-center">
            <button
              onClick={handlePlayClick}
              disabled={isLoading}
              className="group flex flex-col items-center justify-center p-8 rounded-2xl theme-card theme-hover transition-all duration-300"
            >
              {isLoading ? (
                <div className="w-16 h-16 border-4 border-foreground border-t-transparent rounded-full animate-spin mb-4"></div>
              ) : (
                <PlayCircle className="w-16 h-16 text-foreground mb-4 group-hover:text-primary transition-colors" />
              )}
              <span className="text-foreground text-xl font-semibold">
                {isLoading ? 'Loading...' : 'Play Now'}
              </span>
            </button>
          </div>
        )
      ) : (
        /* 正常模式：Grid布局或简单布局 */
        <>
          {/* Left Sidebar - Latest Games (仅在有数据时显示) */}
          {showSidebars && (
            <div className="hidden xl:block w-[388px]">
              <div className="sidebar-content">
                <div className="grid grid-cols-2 gap-2">
                  {/* 左侧边栏：最新游戏 */}
                  {leftGames && leftGames.length > 0 ? (
                    leftGames.slice(0, 12).map((game, index) => (
                      <GameCard
                        key={game.slug || index}
                        game={game}
                        size="small"
                        className="hover:shadow-md transition-shadow duration-200"
                        lazy={index > 1} // 前2个不懒加载
                      />
                    ))
                  ) : (
                    <div className="text-xs theme-text-muted px-2">
                      No games available
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* --- 新的中央核心区开始 --- */}
          <div className="flex-1 flex flex-col min-w-0 max-w-[1000px]">
            {/* Game Area */}
            <div className="w-full relative rounded-lg overflow-hidden bg-muted">
              {!showIframe ? (
                /* Play Now Overlay */
                <div className="relative w-full aspect-video flex items-center justify-center">
                  {/* Background Image with Blur */}
                  {thumbnail && (
                    <div className="absolute inset-0">
                      <Image
                        src={thumbnail}
                        alt={generateGameCoverAlt(getDisplayTitle(), gameSlug)}
                        fill
                        className="object-cover filter blur-sm opacity-60"
                        sizes="1000px"
                        priority
                      />
                      <div className="absolute inset-0 bg-background/40"></div>
                    </div>
                  )}
                  
                  {/* Play Button */}
                  <button
                    onClick={handlePlayClick}
                    disabled={isLoading}
                    className="relative z-10 flex flex-col items-center justify-center p-8 rounded-xl theme-card theme-hover backdrop-blur-sm transition-all duration-300"
                  >
                    {isLoading ? (
                      <div className="w-16 h-16 border-4 border-foreground border-t-transparent rounded-full animate-spin mb-4"></div>
                    ) : (
                      <PlayCircle className="w-16 h-16 text-foreground mb-4 group-hover:text-primary transition-colors" />
                    )}
                    <span className="text-foreground text-xl font-semibold">
                      {isLoading ? 'Loading...' : 'Play Now'}
                    </span>
                    <span className="theme-text-muted text-sm mt-1">
                      Click to start the game
                    </span>
                  </button>
                </div>
              ) : (
                /* Actual iframe */
                <iframe
                  ref={iframeRef}
                  src={iframeUrl}
                  title={`Play ${getDisplayTitle()}`}
                  className="w-full aspect-video border-0"
                  allowFullScreen
                  loading="lazy"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  onError={(e) => {
                    console.error('Failed to load game iframe:', e);
                  }}
                />
              )}
            </div>
            
            {/* Controls Area */}
            <div className="w-full theme-card border theme-border border-t-0 rounded-b-lg">
              <div className="flex items-center justify-between px-4 py-3">
                {/* Left side: Game title (H1) */}
                <div className="flex-1 min-w-0 mr-4">
                  <h1 className="text-lg font-semibold text-foreground truncate">
                    {getDisplayTitle()}
                  </h1>
                </div>

                {/* Right side: Function buttons */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  {/* Play control buttons (optional) */}
                  {showPlayControls && showIframe && (
                    <>
                      <button
                        onClick={() => setIsPlaying(!isPlaying)}
                        className="p-2 rounded-full theme-hover transition-colors theme-text-muted hover:text-foreground"
                        title={isPlaying ? 'Pause' : 'Play'}
                      >
                        {isPlaying ? (
                          <Pause className="w-5 h-5" />
                        ) : (
                          <Play className="w-5 h-5" />
                        )}
                      </button>

                      <button
                        onClick={() => setIsMuted(!isMuted)}
                        className="p-2 rounded-full theme-hover transition-colors theme-text-muted hover:text-foreground"
                        title={isMuted ? 'Unmute' : 'Mute'}
                      >
                        {isMuted ? (
                          <VolumeX className="w-5 h-5" />
                        ) : (
                          <Volume2 className="w-5 h-5" />
                        )}
                      </button>

                      <div className="w-px h-6 bg-border mx-1"></div>
                    </>
                  )}

                  {/* Favorite button */}
                  {gameSlug && gameData && (
                    <BookmarkButton
                      game={gameData}
                      size="medium"
                      className="p-2 rounded-full theme-hover transition-colors theme-text-muted"
                    />
                  )}

                  {/* Share dropdown */}
                  {gameSlug && (
                    <ShareDropdown
                      title={getDisplayTitle()}
                      gameSlug={gameSlug}
                    />
                  )}

                  {/* Fullscreen button */}
                  <button
                    onClick={handleFullscreen}
                    className="p-2 rounded-full theme-hover transition-colors theme-text-muted hover:text-foreground"
                    title="Enter fullscreen"
                  >
                    <Maximize className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          {/* --- 新的中央核心区结束 --- */}

          {/* Right Sidebar - Hot Games (仅在有数据时显示) */}
          {showSidebars && (
            <div className="hidden xl:block w-[388px]">
              <div className="sidebar-content">
                <div className="grid grid-cols-2 gap-2">
                  {/* 右侧边栏：热门游戏 */}
                  {rightGames && rightGames.length > 0 ? (
                    rightGames.slice(0, 12).map((game, index) => (
                      <GameCard
                        key={game.slug || index}
                        game={game}
                        size="small"
                        className="hover:shadow-md transition-shadow duration-200"
                        lazy={index > 1} // 前2个不懒加载
                        tags={index === 0 ? ['hot'] : []} // 第一个游戏显示热门标签
                      />
                    ))
                  ) : (
                    <div className="text-xs theme-text-muted px-2">
                      No games available
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}