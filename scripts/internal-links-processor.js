const fs = require('fs').promises;
const path = require('path');

/**
 * 内部链接处理器
 * 负责在生成的内容中智能添加内部链接
 */
class InternalLinksProcessor {
  constructor(options = {}) {
    this.configPath = options.configPath || path.join(__dirname, '../config/internal-links.json');
    this.linksConfig = null;
    this.options = {
      maxLinksPerKeyword: options.maxLinksPerKeyword || 1, // 每个关键词最多链接次数
      maxTotalLinks: options.maxTotalLinks || 5, // 每篇文章最多总链接数
      caseSensitive: options.caseSensitive || false, // 是否区分大小写
      preserveCase: options.preserveCase !== false, // 是否保持原文大小写
      skipCodeBlocks: options.skipCodeBlocks !== false, // 是否跳过代码块
      ...options
    };
  }

  /**
   * 加载内部链接配置
   */
  async loadConfig() {
    if (this.linksConfig) {
      return this.linksConfig;
    }

    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      this.linksConfig = JSON.parse(configData);
      
      // 预处理配置：按关键词长度排序（长的优先），避免短关键词覆盖长关键词
      this.sortedKeywords = Object.keys(this.linksConfig)
        .sort((a, b) => b.length - a.length);
      
      return this.linksConfig;
    } catch (error) {
      throw new Error(`无法加载内部链接配置: ${error.message}`);
    }
  }

  /**
   * 处理内容并添加内部链接
   * @param {string} content - 要处理的内容
   * @param {Object} context - 上下文信息（可选）
   * @returns {Object} 处理结果
   */
  async processContent(content, context = {}) {
    await this.loadConfig();

    if (!content || typeof content !== 'string') {
      return {
        content: content,
        linksAdded: 0,
        linksMap: {}
      };
    }

    let processedContent = content;
    const linksAdded = {};
    let totalLinksCount = 0;

    // 如果需要跳过代码块，先标记代码块位置
    const codeBlocks = this.options.skipCodeBlocks ? this.findCodeBlocks(content) : [];

    // 按关键词长度排序处理，避免短词覆盖长词
    for (const keyword of this.sortedKeywords) {
      if (totalLinksCount >= this.options.maxTotalLinks) {
        break; // 达到最大链接数限制
      }

      const linkUrl = this.linksConfig[keyword];
      const keywordCount = linksAdded[keyword] || 0;

      if (keywordCount >= this.options.maxLinksPerKeyword) {
        continue; // 该关键词已达到最大链接次数
      }

      const result = this.addLinkForKeyword(
        processedContent, 
        keyword, 
        linkUrl, 
        codeBlocks,
        this.options.maxLinksPerKeyword - keywordCount
      );

      if (result.linksAdded > 0) {
        processedContent = result.content;
        linksAdded[keyword] = (linksAdded[keyword] || 0) + result.linksAdded;
        totalLinksCount += result.linksAdded;
      }
    }

    return {
      content: processedContent,
      linksAdded: totalLinksCount,
      linksMap: linksAdded,
      context: context
    };
  }

  /**
   * 为特定关键词添加链接
   * @param {string} content - 内容
   * @param {string} keyword - 关键词
   * @param {string} linkUrl - 链接URL
   * @param {Array} codeBlocks - 代码块位置数组
   * @param {number} maxLinks - 最大链接数
   * @returns {Object} 处理结果
   */
  addLinkForKeyword(content, keyword, linkUrl, codeBlocks, maxLinks) {
    let linksAdded = 0;
    let processedContent = content;

    // 创建正则表达式
    const flags = this.options.caseSensitive ? 'g' : 'gi';
    const escapedKeyword = this.escapeRegExp(keyword);
    
    // 使用单词边界，确保匹配完整单词
    const regex = new RegExp(`\\b(${escapedKeyword})\\b`, flags);

    processedContent = processedContent.replace(regex, (match, capturedGroup, offset) => {
      // 检查是否已达到最大链接数
      if (linksAdded >= maxLinks) {
        return match;
      }

      // 检查是否在代码块内
      if (this.isInCodeBlock(offset, codeBlocks)) {
        return match;
      }

      // 检查是否已经是链接的一部分
      if (this.isAlreadyLinked(content, offset, match.length)) {
        return match;
      }

      linksAdded++;
      
      // 保持原文大小写
      const displayText = this.options.preserveCase ? match : capturedGroup;
      return `[${displayText}](${linkUrl})`;
    });

    return {
      content: processedContent,
      linksAdded: linksAdded
    };
  }

  /**
   * 查找代码块位置
   * @param {string} content - 内容
   * @returns {Array} 代码块位置数组
   */
  findCodeBlocks(content) {
    const codeBlocks = [];
    
    // 查找行内代码 `code`
    const inlineCodeRegex = /`[^`]+`/g;
    let match;
    while ((match = inlineCodeRegex.exec(content)) !== null) {
      codeBlocks.push({
        start: match.index,
        end: match.index + match[0].length
      });
    }

    // 查找代码块 ```code```
    const blockCodeRegex = /```[\s\S]*?```/g;
    while ((match = blockCodeRegex.exec(content)) !== null) {
      codeBlocks.push({
        start: match.index,
        end: match.index + match[0].length
      });
    }

    return codeBlocks;
  }

  /**
   * 检查位置是否在代码块内
   * @param {number} position - 位置
   * @param {Array} codeBlocks - 代码块数组
   * @returns {boolean}
   */
  isInCodeBlock(position, codeBlocks) {
    return codeBlocks.some(block => 
      position >= block.start && position < block.end
    );
  }

  /**
   * 检查是否已经是链接的一部分
   * @param {string} content - 内容
   * @param {number} offset - 偏移量
   * @param {number} length - 匹配长度
   * @returns {boolean}
   */
  isAlreadyLinked(content, offset, length) {
    // 检查前面是否有 [ 符号
    const beforeChar = content.charAt(offset - 1);
    if (beforeChar === '[') {
      return true;
    }

    // 检查后面是否有 ]( 符号
    const afterChars = content.substr(offset + length, 2);
    if (afterChars === '](') {
      return true;
    }

    // 更全面的检查：查看是否在现有的markdown链接中
    const beforeText = content.substring(Math.max(0, offset - 100), offset);
    const afterText = content.substring(offset + length, Math.min(content.length, offset + length + 100));
    
    // 检查是否在 [text](url) 结构中
    const linkPattern = /\[[^\]]*$/;
    const linkEndPattern = /^[^\]]*\]\([^)]*\)/;
    
    if (linkPattern.test(beforeText) && linkEndPattern.test(afterText)) {
      return true;
    }

    return false;
  }

  /**
   * 转义正则表达式特殊字符
   * @param {string} string - 要转义的字符串
   * @returns {string}
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 获取配置信息
   * @returns {Object}
   */
  getConfig() {
    return {
      configPath: this.configPath,
      options: this.options,
      keywordsCount: this.linksConfig ? Object.keys(this.linksConfig).length : 0
    };
  }

  /**
   * 验证配置文件
   * @returns {Object} 验证结果
   */
  async validateConfig() {
    try {
      await this.loadConfig();
      
      const issues = [];
      const stats = {
        totalKeywords: Object.keys(this.linksConfig).length,
        uniqueUrls: [...new Set(Object.values(this.linksConfig))].length,
        duplicateKeywords: [],
        invalidUrls: []
      };

      // 检查重复关键词（不区分大小写）
      const keywordMap = {};
      for (const keyword of Object.keys(this.linksConfig)) {
        const lowerKeyword = keyword.toLowerCase();
        if (keywordMap[lowerKeyword]) {
          stats.duplicateKeywords.push({
            original: keywordMap[lowerKeyword],
            duplicate: keyword
          });
        } else {
          keywordMap[lowerKeyword] = keyword;
        }
      }

      // 检查URL格式
      for (const [keyword, url] of Object.entries(this.linksConfig)) {
        if (!url.startsWith('/') && !url.startsWith('http')) {
          stats.invalidUrls.push({ keyword, url });
        }
      }

      if (stats.duplicateKeywords.length > 0) {
        issues.push(`发现 ${stats.duplicateKeywords.length} 个重复关键词`);
      }

      if (stats.invalidUrls.length > 0) {
        issues.push(`发现 ${stats.invalidUrls.length} 个无效URL`);
      }

      return {
        valid: issues.length === 0,
        issues,
        stats
      };

    } catch (error) {
      return {
        valid: false,
        issues: [`配置文件错误: ${error.message}`],
        stats: null
      };
    }
  }
}

module.exports = InternalLinksProcessor; 